# Product Requirements Documentation (PRD)

This repository provides structured templates and comprehensive guidelines for creating effective Product Requirements Documentation (PRD). It follows industry best practices and Agile methodologies to help teams create clear, concise, and user-centric product documentation.

## Getting Started

### Prerequisites
- GitHub account
- Git installed on your local machine
- Cursor AI IDE installed
- Basic understanding of Markdown

### Setup Instructions

1. **Fork the Repository**
   - Navigate to the repository's GitHub page
   - Click the "Fork" button in the top-right corner
   - Select your GitHub account as the destination

2. **Clone to Local Machine**
   ```bash
   # Replace YOUR-USERNAME with your GitHub username
   git clone https://github.com/YOUR-USERNAME/prd-templates.git
   cd prd-templates
   ```

3. **Open in Cursor AI**
   - Launch Cursor AI
   - Go to File > Open Folder
   - Select the cloned `prd-templates` directory

4. **Read the Documentation Guide**
   - Start with the `guide/` directory
   - Review each section of the PRD structure
   - Familiarize yourself with the templates and examples

### Using the Templates

1. **Create Your Project Directory**
   ```bash
   # Create a new branch for your project
   git checkout -b my-project-name
   
   # Create a new directory for your project
   mkdir projects/my-project-name
   ```

2. **Edit Documents with Cursor AI**
   - Open the relevant template files
   - Use Cursor AI to help generate and refine content
   - Follow the structured format for each section:
     - Product Overview
     - User Personas
     - Use Cases/User Stories
     - Functional Requirements
     - Non-functional Requirements
     - Assumptions & Constraints
     - (Optional) Out of Scope
     - (Optional) Appendices

   > **Note**: Use the "Outline" view in Cursor AI to help you structure your PRD.

3. **Preview Markdown**
   In Cursor AI or VS Code:
   - Side-by-Side Preview: `Cmd+K V` (Mac) or `Ctrl+K V` (Windows/Linux)
   - Full-Tab Preview: `Cmd+Shift+V` (Mac) or `Ctrl+Shift+V` (Windows/Linux)

### Committing Changes

1. **Stage Your Changes**
   ```bash
   git add projects/my-project-name/
   ```

2. **Commit Your Changes**
   ```bash
   git commit -m "Add PRD for my-project-name"
   ```

3. **Push to GitHub**
   ```bash
   git push origin my-project-name
   ```

## Repository Structure

```
prd-templates/
├── guide/                  # Comprehensive PRD writing guidelines
├── templates/             # Base templates for different PRD types
├── examples/             # Example PRDs for reference
└── projects/            # Directory for your PRD projects
```

## Best Practices

- Keep each section focused and concise
- Use clear, unambiguous language
- Link requirements to user stories
- Include measurable acceptance criteria
- Add diagrams and images (as embedded links)to help visualize the product
- Regularly update and maintain your PRD
- Use Markdown formatting consistently
- Commit changes frequently with clear messages

## Contributing

1. Create a new branch for your changes
2. Make your updates
3. Submit a pull request with a clear description
4. Wait for review and approval

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions:

1. Check the documentation in the `guide/` directory
2. Review existing GitHub Issues
3. Create a new Issue if needed
4. Ask the IXO Guru Oracle for help and add conribute by adding any missing information to Guru's knowledge base

---
*Note: This repository is designed to work best with Cursor AI for intelligent assistance in PRD creation and refinement.*
