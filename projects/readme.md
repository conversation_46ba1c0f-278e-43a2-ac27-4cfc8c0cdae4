# Projects Directory

This directory contains individual PRD projects created using the templates. Each project should be in its own subdirectory with a descriptive name.

## Structure

```
projects/
├── project-name/          # Your project directory
│   ├── prd.md            # Main PRD document
│   └── assets/           # Supporting files (diagrams, images, etc.)
└── readme.md             # This file
```

## Guidelines

1. Create a new directory for each project
2. Use kebab-case for directory names (e.g., `my-awesome-project`)
3. Include a main `prd.md` file using the templates
4. Store supporting files in an `assets` subdirectory
5. Follow the standard PRD structure from the main templates

---
*See the root readme for detailed instructions on creating and managing projects.*
