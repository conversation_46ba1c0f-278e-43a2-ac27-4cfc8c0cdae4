# Product Requirements Document

## Table of Contents

- [1 Product Overview and Vision](#1-product-overview-and-vision)
- [2 Background and Strategic Fit](#2-background-and-strategic-fit)
- [3 User Personas and Needs](#3-user-personas-and-needs)
- [4 Product Scope and Features](#4-product-scope-and-features)
- [5 Assumptions and Constraints](#5-assumptions-and-constraints)
- [6 User Experience and Design](#6-user-experience-and-design)
- [7 Success Metrics and Release Criteria](#7-success-metrics-and-release-criteria)
- [8 Delivery Milestones](#8-delivery-milestones)
- [9 Out of Scope Items](#9-out-of-scope-items)
- [10 Open Questions and Issues](#10-open-questions-and-issues)

# 1. Product Overview and Vision

<details>
<summary>Click to expand</summary>

The USSD Customer Interface is a mobile-based solution that enables SupaMoto customers in rural Zambia, Malawi, and Mozambique to manage their energy needs without requiring smartphones or internet access. By leveraging simple USSD technology, customers can top up wallets, purchase fuel pellets, schedule deliveries, and track orders directly from basic mobile phones.

**Problem:** Many SupaMoto customers lack smartphones and reliable internet access, resulting in inefficient manual ordering processes, payment verification delays, and limited visibility into order status.

**Solution:** A comprehensive USSD interface that integrates with mobile money services, ERP systems, and delivery management platforms to provide real-time transactions, secure authentication, and self-service capabilities.

**Target Users:** Rural and peri-urban customers in Zambia, Malawi, and Mozambique who rely on basic mobile phones for essential services and use SupaMoto's clean cooking fuel products.

**Key Benefits:**

- Increased accessibility through multi-language support and basic phone compatibility
- Improved financial control with real-time wallet updates and transaction history
- Enhanced customer experience through order tracking and automated scheduling
- Reduced operational costs by minimizing manual call center interventions

This solution directly supports SupaMoto's business goals of expanding market reach, improving operational efficiency, and enhancing customer satisfaction while addressing the digital divide in emerging markets.

</details>

# 2. Background and Strategic Fit

<details>
<summary>Click to expand</summary>

## Market Context

SupaMoto serves customers across Zambia, Malawi, and Mozambique with clean cooking fuel solutions, but faces significant challenges in customer accessibility. Our market research indicates that over 70% of our customer base relies on basic mobile phones without internet access, creating a digital divide that hampers service delivery and customer satisfaction. Call center data shows that 65% of incoming calls are for basic transactions like ordering and balance inquiries, which could be automated through self-service channels.

## Problem Statement

Current customer interactions require manual intervention through call centers, resulting in operational inefficiencies, delayed order processing, and limited visibility into transaction status. Without a digital self-service option compatible with basic phones, customers experience:

- Delays of up to 48 hours for order confirmations
- No real-time visibility into wallet balances or transaction history
- Limited ability to schedule deliveries at convenient times
- Frustration from language barriers during call center interactions

## Market Opportunity

The widespread adoption of mobile money services in our target markets (92% penetration in urban Zambia, 78% in rural areas) presents an opportunity to leverage existing infrastructure for improved service delivery. Competitor analysis reveals that no other clean cooking solution provider offers comprehensive USSD-based services in these markets, creating a potential competitive advantage and differentiation point.

## Strategic Alignment

This USSD Customer Interface initiative directly supports SupaMoto's 2025 strategic objectives:

1. **Market Expansion**: By removing technological barriers to service access, we can increase our addressable market by an estimated 40% in rural areas.

2. **Operational Efficiency**: Automating routine transactions is projected to reduce call center volume by 65% and decrease order processing costs by 30%.

3. **Customer Retention**: Improved service accessibility and transparency aligns with our goal to increase customer lifetime value by 25% through enhanced satisfaction and engagement.

4. **Digital Transformation**: This project represents the first phase of our broader digital strategy, establishing the foundation for future innovations in customer engagement and service delivery.

The timing is optimal as mobile money adoption has reached critical mass in our markets, and recent regulatory changes in Zambia have reduced transaction fees, making digital financial services more accessible to our target demographic.

</details>

# 3. User Personas and Needs

<details>
<summary>Click to expand</summary>

## Primary Persona: Rural Retailer Mutinta

**Demographics & Characteristics:**

- 42-year-old small shop owner in rural Zambia
- Limited formal education (completed primary school)
- Owns a basic feature phone, not a smartphone
- Limited internet access in her village
- Speaks Bemba primarily, with basic English proficiency
- Manages household cooking for a family of five
- Monthly income: 1,200-1,800 Zambian Kwacha (~$60-90 USD)
- Uses SupaMoto pellets as primary cooking fuel for both home and small food preparation business

**Goals:**

1. Maintain consistent fuel supply to avoid business interruptions
2. Minimize time spent on ordering and managing fuel supplies
3. Track expenses accurately for business planning
4. Maximize value from limited financial resources
5. Build savings for future business expansion

**Pain Points:**

1. Must travel to agent locations or make lengthy calls to place orders
2. Often experiences delays in order confirmation and delivery
3. Struggles to track wallet balance and transaction history
4. Faces language barriers when communicating with support staff
5. Limited visibility into order status causes anxiety and business disruption
6. Difficulty planning for fuel expenses due to manual payment processes

**Key Use Cases:**

1. Check wallet balance before making purchase decisions
2. Top up wallet using mobile money when funds are available
3. Purchase standard quantities of pellets (typically 20kg bags)
4. Schedule deliveries around business operating hours
5. Track order status to plan business activities
6. Review transaction history for business expense tracking

## Secondary Persona: Urban Agent Chishimba

**Demographics & Characteristics:**

- 35-year-old SupaMoto agent in peri-urban Lusaka
- Completed secondary education
- Owns a basic smartphone with intermittent data access
- Fluent in English and Nyanja
- Manages a small distribution point serving 50-75 customers
- Monthly income: 3,000-4,500 Zambian Kwacha (~$150-225 USD)
- Serves as intermediary between SupaMoto and end customers

**Goals:**

1. Efficiently process customer orders and payments
2. Maintain accurate records of transactions
3. Build customer loyalty through reliable service
4. Maximize commission earnings through increased sales volume
5. Minimize administrative workload

**Pain Points:**

1. Manual record-keeping is time-consuming and error-prone
2. Delayed payment confirmations affect customer trust
3. Limited visibility into delivery schedules creates customer service challenges
4. Difficulty managing cash flow due to payment processing delays
5. Struggles to track customer preferences and purchase patterns

While our USSD interface will primarily serve end customers like Mutinta, we recognize that agents like Chishimba will interact with the system and benefit from its implementation through reduced administrative burden and improved customer satisfaction.

</details>

# 4. Product Scope and Features

<details>
<summary>Click to expand</summary>

## Core Features

### 1. USSD Menu Navigation System (MUST HAVE)

**Description:** A structured menu system accessible via USSD short codes that allows users to navigate through all available services using basic feature phones.

**User Problem:** Customers without smartphones need a simple way to access SupaMoto services without requiring internet connectivity or advanced devices.

**Acceptance Criteria:**

- Users can access the system by dialing a simple USSD code (e.g., \*123#)
- Menu options are displayed in numbered format for easy selection
- Navigation supports "back" and "home" options at each level
- System responds within 3 seconds of user input
- Sessions timeout after 2 minutes of inactivity with appropriate messaging

### 2. Multi-Language Support (MUST HAVE)

**Description:** Language selection option that allows users to interact with the USSD system in their preferred language.

**User Problem:** Many customers face language barriers when using English-only systems, limiting accessibility and causing confusion.

**Acceptance Criteria:**

- System offers language selection at first use (English, Bemba, Nyanja)
- Language preference is saved for future sessions
- All menu items and messages are correctly translated in each supported language
- Users can change language preference at any time from the main menu

### 3. Wallet Balance Inquiry (MUST HAVE)

**Description:** Feature allowing customers to check their current SupaMoto wallet balance.

**User Problem:** Customers need to know their available funds before making purchase decisions but currently have no self-service way to access this information.

**Acceptance Criteria:**

- Balance is displayed in local currency with correct formatting
- System shows last transaction date alongside balance
- Balance updates reflect within 60 seconds of any transaction
- Zero or negative balances include appropriate messaging about top-up options

### 4. Mobile Money Wallet Top-Up (MUST HAVE)

**Description:** Integration with mobile money services allowing customers to add funds to their SupaMoto wallet.

**User Problem:** Customers need a convenient way to fund their accounts without traveling to agent locations or making bank transfers.

**Acceptance Criteria:**

- System connects with major mobile money providers (MTN, Airtel, Zamtel)
- Users can select from predefined amounts or enter custom amount
- Confirmation message is required before processing
- Transaction receipt is provided via SMS upon completion
- Wallet balance updates within 60 seconds of successful payment

### 5. Fuel Pellet Ordering (MUST HAVE)

**Description:** Feature allowing customers to purchase fuel pellets in various quantities directly through USSD.

**User Problem:** Ordering fuel currently requires phone calls or visits to agents, causing delays and inconvenience.

**Acceptance Criteria:**

- System displays available pellet bag sizes (5kg, 20kg, 30kg, 50kg) with prices
- Users can select quantity of each size
- Total cost is calculated and displayed before confirmation
- System verifies sufficient wallet balance before processing
- Order confirmation number is provided upon successful purchase
- Receipt is sent via SMS

### 6. Delivery Scheduling (SHOULD HAVE)

**Description:** Option to schedule delivery date and time for pellet orders.

**User Problem:** Customers need deliveries to align with their availability but currently have limited control over delivery timing.

**Acceptance Criteria:**

- System offers available delivery slots within the next 7 days
- Users can select morning or afternoon delivery windows
- Scheduling options respect operational delivery constraints
- Confirmation of selected slot is provided
- Reminder SMS sent one day before scheduled delivery

### 7. Order Tracking (SHOULD HAVE)

**Description:** Feature allowing customers to check the status of their pending orders.

**User Problem:** Customers experience anxiety and business disruption when they cannot determine when their fuel will arrive.

**Acceptance Criteria:**

- System displays list of recent orders with status (processing, dispatched, delivered)
- Each order shows expected delivery date when applicable
- Order details include quantity, cost, and payment status
- Status updates within 15 minutes of status change in backend systems

### 8. Transaction History (SHOULD HAVE)

**Description:** Access to historical wallet transactions and order records.

**User Problem:** Customers need to track their spending and verify past transactions for business record-keeping.

**Acceptance Criteria:**

- System displays last 5 transactions by default
- Each transaction shows date, type, amount, and resulting balance
- Option to request older transactions via SMS
- Transactions are categorized by type (top-up, purchase, refund)

### 9. PIN Authentication (MUST HAVE)

**Description:** Security feature requiring PIN verification for sensitive transactions.

**User Problem:** Customers need protection against unauthorized use if their phone is lost or borrowed.

**Acceptance Criteria:**

- PIN setup required during first transaction
- PIN verification required for purchases and wallet management
- Option to reset PIN through verified alternate contact method
- System locks after 3 failed attempts, requiring agent assistance to unlock

### 10. Feedback Collection (NICE-TO-HAVE)

**Description:** Simple survey mechanism to collect customer feedback on service quality.

**User Problem:** Customers have no easy way to report issues or suggest improvements to the service.

**Acceptance Criteria:**

- Short survey (max 3 questions) offered after completed transactions
- Rating scale uses simple numeric values (1-5)
- Option to provide text feedback via SMS
- Feedback is tagged with customer ID for follow-up

## Out of Scope (Future Considerations)

- Loyalty program point tracking and redemption
- Agent-specific interfaces for inventory management
- Integration with stove monitoring systems
- Promotional messaging and marketing campaigns
- Advanced analytics and spending pattern insights

</details>

# 5. Assumptions and Constraints

<details>
<summary>Click to expand</summary>

## Assumptions

### User Assumptions

- Most users have access to basic feature phones with USSD capability
- Users are familiar with USSD menu navigation from other services (banking, mobile money)
- Users will prefer self-service options over calling customer support when available
- Users have active mobile money accounts with sufficient funds for transactions
- Users will be willing to learn a new system if it saves them time and effort
- Language preferences will be primarily distributed among English, Bemba, and Nyanja

### Business Assumptions

- SupaMoto's existing ERP system can be integrated with the USSD platform
- Mobile money providers will maintain their current transaction fee structure
- Agent network will continue to serve as backup for customers who need assistance
- Order fulfillment capacity can handle increased volume from self-service channels
- Customer support team can manage PIN reset and account recovery processes
- The project will generate sufficient ROI through reduced operational costs

### Technical Assumptions

- Mobile network coverage is sufficient in target areas for USSD transactions
- USSD session timeout (2 minutes) is adequate for most user interactions
- Mobile money APIs will provide real-time transaction confirmation
- ERP system can process orders in real-time from the USSD interface
- SMS delivery is reliable enough for transaction receipts and notifications
- Database can handle concurrent transactions during peak usage periods

## Constraints

### Technical Constraints

- USSD menus are limited to 160 characters per screen
- USSD sessions have a maximum duration of 2 minutes before timeout
- System must work on all basic feature phones without requiring internet connectivity
- Integration must support all major mobile money providers in Zambia (MTN, Airtel, Zamtel)
- Response time for any USSD menu interaction must be under 5 seconds
- System must handle network interruptions gracefully with appropriate error messaging
- Database must maintain transaction integrity even during connection failures

### Business Constraints

- Solution must be operational before the start of peak season (May 2025)
- Implementation budget is capped at $75,000 for initial development
- System must reduce call center volume by at least 40% to meet ROI targets
- All transaction fees must be transparent to users before confirmation
- Solution must accommodate future expansion to Malawi and Mozambique markets

### Regulatory & Compliance Constraints

- System must comply with Zambia's Data Protection Act (2021)
- Mobile money integrations must meet Central Bank of Zambia's security requirements
- PIN authentication must follow industry standard encryption practices
- System must maintain audit logs of all financial transactions for 7 years
- Privacy policy must be accessible to users and consent obtained during first use
- Solution must comply with ISO 27001 security standards for financial transactions

### Dependencies

- Mobile network operators must approve USSD short code allocation
- ERP vendor must provide API access for integration
- Mobile money providers must approve integration with their payment systems
- SMS gateway service must be contracted for notification delivery
- Customer data migration from existing systems must be completed before launch

</details>

# 6. User Experience and Design

<details>
<summary>Click to expand</summary>

## USSD Interface Design Principles

The USSD Customer Interface follows a simple, text-based menu structure optimized for basic feature phones. The design prioritizes clarity, efficiency, and ease of use for customers with limited technical literacy. All interactions must be accomplishable with numeric inputs and require minimal typing.

## Key User Flows

### 1. System Access & Navigation

Users access the system by dialing the USSD short code (\*123#). The main menu presents numbered options for all primary functions. Each subsequent screen maintains consistent navigation patterns:

- Numbered options (1-9) for selections
- "0" to return to previous menu
- "#" to return to main menu
- "\*" to exit the system

**Design Note:** Menu options are limited to 8 per screen maximum to prevent overwhelming users and ensure all options fit within the 160-character USSD screen limitation.

### 2. Language Selection Flow

On first use, customers are prompted to select their preferred language:

1. Welcome screen with language options (1-English, 2-Bemba, 3-Nyanja)
2. User selects preferred language by entering corresponding number
3. System confirms selection and saves preference
4. User is directed to main menu in selected language

**UX Requirement:** Language selection must be the first interaction for new users and must be accessible from the settings menu for existing users.

### 3. Wallet Top-Up Flow

The wallet top-up process follows this sequence:

1. User selects "Top Up Wallet" from main menu
2. System displays mobile money provider options
3. User selects provider
4. System presents predefined amount options and custom amount option
5. User selects or enters amount
6. System displays confirmation screen with amount and fees
7. User confirms by entering PIN
8. System initiates mobile money transaction
9. Confirmation screen displays with transaction reference

**Design Note:** See [Figure 1: Mobile Money Top-Up Flow Diagram](link-to-diagram) for the complete interaction sequence.

### 4. Pellet Purchase Flow

The purchase flow is designed to minimize steps while ensuring clarity:

1. User selects "Buy Pellets" from main menu
2. System displays available pellet bag sizes with prices
3. User selects desired size
4. System prompts for quantity
5. User enters quantity
6. System displays order summary with total cost
7. User confirms purchase by entering PIN
8. System processes payment from wallet
9. Confirmation screen shows order number and delivery information

**UX Requirement:** Order summaries must clearly display quantity, unit price, total cost, and wallet balance after purchase.

### 5. Order Tracking Flow

Order tracking is designed for simplicity:

1. User selects "Track Order" from main menu
2. System displays list of recent orders with status indicators
3. User selects specific order by entering corresponding number
4. System displays detailed order information including status, expected delivery date, and payment details

**Design Note:** See [Figure 2: Order Status Screen Mockup](link-to-mockup) for the information layout.

## Design Considerations

### Character Limitations

All USSD screens are limited to 160 characters. Menu options and instructions are written concisely to fit within this constraint while maintaining clarity. Where necessary, information is split across multiple screens with clear pagination indicators.

### Response Times

The system is designed to respond within 3 seconds for any interaction. Loading indicators are displayed for operations that may take longer (such as payment processing).

### Error Handling

Error messages are written in simple, non-technical language that clearly explains:

1. What went wrong
2. What the user should do next
3. How to get help if needed

**Example:** "Network problem. Your payment was not processed. Try again or call 0800-123-456 for help."

### Accessibility Considerations

- All text uses simple, clear language (maximum 6th-grade reading level)
- Important information is positioned at the beginning of messages
- Critical actions require confirmation to prevent errors
- Session timeouts include warnings at 90 seconds
- Error recovery paths are provided for all transactions

## User Testing Insights

Initial paper prototyping with 12 potential users in rural Zambia revealed:

- Users preferred numbered menus over text entry
- Transaction confirmations with clear fee disclosure were highly valued
- Users struggled with technical terms like "authenticate" (changed to "enter PIN")
- Order tracking was the most requested feature after wallet management

These insights have been incorporated into the current design approach. Additional user testing will be conducted with the working prototype.

## Design Resources

- [USSD Menu Structure Map](link-to-resource)
- [User Flow Diagrams](link-to-resource)
- [Error Message Library](link-to-resource)
- [Content Style Guide](link-to-resource)

</details>

# 7. Success Metrics and Release Criteria

<details>
<summary>Click to expand</summary>

## Product Success Metrics

### User Adoption & Engagement

- 30% of existing SupaMoto customers registered on USSD platform within 3 months of launch
- 50% of registered users complete at least one transaction per month
- Average of 2.5 interactions per user per month
- 80% of pellet orders placed through USSD rather than call center by month 6

### Operational Efficiency

- 40% reduction in call center volume for routine transactions
- 25% decrease in order processing time from request to confirmation
- 60% reduction in payment reconciliation errors
- 30% reduction in agent time spent on manual transaction recording

### Customer Satisfaction

- Net Promoter Score (NPS) of 40+ from USSD platform users
- Customer satisfaction rating of 4.2/5 or higher via post-transaction surveys
- Less than 5% of USSD sessions abandoned before completion
- 90% of users able to complete their intended task without support

### Financial Impact

- 20% increase in average transaction frequency per customer
- 15% reduction in cost-per-transaction compared to agent-assisted methods
- ROI breakeven within 12 months of launch
- 10% increase in overall pellet sales volume

## Release Criteria

### Functionality Requirements

- All "MUST HAVE" features fully implemented and functional
- Complete end-to-end transaction flow verified for all supported mobile money providers
- Multi-language support functioning correctly for all menu items
- PIN authentication and security measures fully operational
- Order tracking accurately reflects backend system status

### Performance Standards

- USSD menu response time under 3 seconds for 99% of requests
- System handles 100 concurrent sessions without degradation
- Mobile money transaction processing completes within 30 seconds
- SMS notifications delivered within 2 minutes of trigger events
- System maintains functionality during 95% network coverage conditions

### Reliability & Stability

- 99.5% uptime during business hours (8am-8pm)
- No critical failures during 72-hour continuous load testing
- Successful recovery from simulated network interruptions without data loss
- Zero wallet balance discrepancies in reconciliation testing
- All transactions maintain integrity during failover testing

### Security & Compliance

- Passes security penetration testing with no critical vulnerabilities
- Complies with Zambia Data Protection Act requirements
- PIN encryption meets banking-grade standards
- Transaction logs properly maintained and secured
- Mobile money integration approved by all partner providers
- Passes ISO 27001 security controls assessment

### Usability Validation

- Successful completion of key tasks by 85% of test users without assistance
- Average task completion time under 90 seconds for standard transactions
- Less than 10% error rate on first-time user interactions
- 90% of test users able to navigate back to main menu from any point
- Successful user testing with low-literacy participants

### Support Readiness

- Customer support team fully trained on troubleshooting procedures
- Support documentation completed and approved
- Monitoring dashboard operational with appropriate alerts configured
- Disaster recovery procedures documented and tested
- Agent training materials developed and distributed

## Minimum Viable Product (MVP) Requirements

For initial release, the following capabilities are non-negotiable:

1. USSD menu navigation system
2. Wallet balance inquiry
3. Mobile money wallet top-up (at least one provider)
4. Basic pellet ordering (standard quantities)
5. PIN authentication
6. Transaction receipts via SMS
7. English language support (additional languages can follow)

## Launch Approval Process

Final release approval requires sign-off from:

1. Product Manager
2. Technical Lead
3. Customer Support Manager
4. Security Officer
5. Finance Representative
6. Executive Sponsor

Each stakeholder will verify their respective criteria have been met before the system goes live.

</details>

# 8. Delivery Milestones

<details>
<summary>Click to expand</summary>

## Project Timeline Overview

The USSD Customer Interface will be developed over a 6-month period, with phased releases to ensure quality and manage risk. We will follow a two-week sprint cadence with key milestones as outlined below.

## Key Milestones

### Phase 1: Discovery & Planning

- **March 24, 2025:** Project kickoff and requirements finalization
- **April 7, 2025:** Technical architecture approved
- **April 22, 2025:** USSD provider selection and contract finalized
- **May 2, 2025:** Design specifications and user flows completed

### Phase 2: Core Development (MVP)

- **May 7, 2025:** Development Sprint 1 begins
  - USSD menu navigation system
  - Language selection functionality
  - Basic wallet balance inquiry
- **May 21, 2025:** Development Sprint 2 begins
  - PIN authentication system
  - Mobile money integration (MTN provider)
  - Basic error handling
- **June 4, 2025:** Development Sprint 3 begins
  - Basic pellet ordering functionality
  - SMS notification system
  - Transaction receipts
- **June 18, 2025:** MVP Feature Complete
  - All core functionality implemented
  - Internal testing begins

### Phase 3: Testing & Refinement

- **June 25, 2025:** QA and security testing complete
- **July 2, 2025:** Pilot launch with 50 selected customers in Lusaka
  - Two-week pilot period with daily monitoring
  - Focus on transaction success rate and system stability
- **July 16, 2025:** Pilot feedback review and system adjustments
- **July 30, 2025:** Development Sprint 4 begins
  - Critical fixes from pilot feedback
  - Additional mobile money providers integration
  - Order tracking functionality

### Phase 4: Full Launch & Expansion

- **August 13, 2025:** Full Launch in Zambia
  - All "MUST HAVE" features available
  - Customer support team fully trained
  - Marketing campaign begins
- **August 27, 2025:** Development Sprint 5 begins
  - Transaction history feature
  - Delivery scheduling functionality
  - Performance optimizations
- **September 10, 2025:** Development Sprint 6 begins
  - Feedback collection mechanism
  - Additional language support (Nyanja)
  - Enhanced error recovery
- **September 24, 2025:** Phase 1 Complete
  - All planned features deployed
  - Performance and usage metrics review
  - Planning for Malawi expansion begins

## Dependencies & Critical Path

1. **External Dependencies:**

   - USSD short code allocation from mobile network operators (4-6 week lead time)
   - Mobile money API access approval from providers (3-4 week process)
   - ERP system API documentation and access (required by May 7)

2. **Internal Dependencies:**
   - Security team review and approval (required before pilot)
   - Customer support training completion (required 2 weeks before full launch)
   - Marketing materials and customer education content (required 3 weeks before launch)

## Post-Launch Support & Enhancement

Following the initial launch, we will transition to a maintenance and enhancement phase with monthly releases:

- **October 2025:** Malawi market expansion
- **November 2025:** Loyalty program integration
- **Q1 2026:** Mozambique market expansion

## Contingency Planning

We have built in the following buffers to accommodate unexpected challenges:

- One-week buffer between MVP completion and pilot launch
- Two-week stabilization period between pilot and full launch
- Ability to extend pilot phase if critical issues are identified

The timeline will be reviewed and updated at the end of each sprint to reflect current progress and any necessary adjustments.

</details>

# 9. Out of Scope Items

<details>
<summary>Click to expand</summary>

The following features and functionality have been explicitly excluded from the initial release of the USSD Customer Interface. While some may be considered for future iterations, they are not part of the current development plan.

## Excluded Features

- **Smartphone Application** - A companion smartphone app is not included in this release. The focus is exclusively on USSD technology to ensure accessibility for customers with basic feature phones.

- **Web Portal for Customers** - No web-based interface for customers will be developed in this phase. Future iterations may explore this for customers with internet access.

- **Agent Management System** - While agents will benefit from reduced manual processing, specific tools for agent inventory management or commission tracking are not included in this release.

- **Stove Monitoring Integration** - Integration with SupaMoto's stove monitoring systems is out of scope. This would require additional IoT infrastructure not aligned with the current USSD focus.

- **Credit Scoring & Lending** - Although wallet management is included, any credit provision or "buy now, pay later" functionality is excluded from this release due to regulatory complexity.

- **Promotional Campaigns & Discounts** - The system will not support complex promotional campaigns or discount codes in the initial release. Standard pricing will apply to all transactions.

- **Automated Customer Support** - While basic FAQs may be accessible via USSD, comprehensive automated support or chatbot functionality is not included in this release.

- **Loyalty Points Redemption** - Although future versions may track loyalty, the redemption of points or rewards is out of scope for the initial release.

- **Multi-Product Ordering** - The system will only support fuel pellet ordering. Other SupaMoto products (stoves, accessories) cannot be purchased through the USSD interface in this release.

- **Advanced Analytics Dashboard** - Detailed customer behavior analytics and reporting tools for business intelligence are excluded from this phase.

## Rationale for Exclusions

These items have been excluded to maintain focus on core functionality that delivers the highest value to customers with limited technology access. By prioritizing essential features like wallet management, basic ordering, and transaction tracking, we can deliver a reliable solution within the planned timeline and budget constraints.

Some of these features may be reconsidered based on customer feedback and business priorities after the initial launch and adoption period.

</details>

# 10. Open Questions and Issues

<details>
<summary>Click to expand</summary>

The following questions and issues remain unresolved at the time of writing this PRD. These items require further investigation, discussion, or decisions before implementation. Each item includes the impact on the project and the owner responsible for resolution.

## Technical Questions

1. **USSD Session Timeout Limits:** What are the exact timeout limitations imposed by each mobile network operator in Zambia?

   - **Impact:** May affect the design of multi-step transactions and session recovery
   - **Owner:** Technical Lead
   - **Resolution Timeline:** Before Sprint 1 (April 7, 2025)

2. **Mobile Money API Reliability:** What is the expected uptime and response time for each mobile money provider's API?

   - **Impact:** May require additional error handling and fallback mechanisms
   - **Owner:** Integration Engineer
   - **Resolution Timeline:** Before Sprint 2 (May 21, 2025)

3. **ERP Integration Approach:** Should we use direct database access or API-only integration with the existing ERP system?
   - **Impact:** Affects system architecture and data synchronization strategy
   - **Owner:** Technical Architect & ERP Administrator
   - **Resolution Timeline:** Technical architecture approval (April 7, 2025)

## Business & Product Questions

4. **Transaction Fee Structure:** Will SupaMoto absorb mobile money transaction fees or pass them to customers?

   - **Impact:** Affects pricing display, wallet calculations, and customer messaging
   - **Owner:** Finance Manager & Product Manager
   - **Resolution Timeline:** Before Sprint 2 (May 21, 2025)

5. **Order Modification Window:** What is the maximum timeframe after placing an order that customers should be allowed to modify or cancel it?

   - **Impact:** Affects order management workflow and business rules
   - **Owner:** Operations Manager
   - **Resolution Timeline:** Before Sprint 3 (June 4, 2025)

6. **Minimum Order Quantities:** Should we enforce minimum order quantities for delivery efficiency?
   - **Impact:** Affects order validation rules and customer messaging
   - **Owner:** Sales Manager
   - **Resolution Timeline:** Design specifications completion (May 2, 2025)

## User Experience Questions

7. **PIN Reset Process:** What is the secure yet accessible process for customers who forget their PIN?

   - **Impact:** Affects account recovery flow and support requirements
   - **Owner:** Security Officer & Customer Support Manager
   - **Resolution Timeline:** Before Sprint 2 (May 21, 2025)

8. **Language Preference Default:** Should we determine default language based on SIM region or always prompt for selection?

   - **Impact:** Affects first-time user experience and onboarding flow
   - **Owner:** Product Manager
   - **Resolution Timeline:** Design specifications completion (May 2, 2025)

9. **Transaction Receipt Format:** What information must be included in SMS receipts to satisfy both customer needs and regulatory requirements?
   - **Impact:** Affects notification system design and compliance
   - **Owner:** Legal Advisor & Product Manager
   - **Resolution Timeline:** Before Sprint 3 (June 4, 2025)

## Operational & Scaling Questions

10. **Agent Role in USSD System:** How will agents interact with or support the USSD system?

    - **Impact:** May require additional features or access controls
    - **Owner:** Agent Network Manager
    - **Resolution Timeline:** Before Sprint 4 (July 30, 2025)

11. **Malawi/Mozambique Differences:** What country-specific adaptations will be needed for expansion markets?

    - **Impact:** May affect design decisions to ensure future compatibility
    - **Owner:** International Operations Manager
    - **Resolution Timeline:** Before Phase 1 completion (September 24, 2025)

12. **Disaster Recovery Requirements:** What are the specific recovery time objectives (RTO) and recovery point objectives (RPO) for the system?
    - **Impact:** Affects infrastructure design and backup procedures
    - **Owner:** IT Manager
    - **Resolution Timeline:** Before QA and security testing (June 25, 2025)

These open questions will be addressed according to the timelines specified. The PRD will be updated as decisions are made and questions are resolved.

</details>
