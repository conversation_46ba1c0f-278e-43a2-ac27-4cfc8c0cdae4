High-Level Business Goals Document – USSD Customer Interface Project

SupaMoto & IXO Collaboration

Date: March 2025
Prepared By: Senior Business Analyst
Stakeholders:
• SupaMoto (ECS) Team: Matthias, Customer Support, Agents, Finance & Operations
• IXO Team: <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>
• Key Business Areas: Mobile Money Transactions, Order Scheduling, Wallet Management, ERP Integration

⸻

1. Business Goals and Objectives

The USSD Customer Interface Project aims to provide seamless, accessible financial and operational interactions for customers who rely on basic mobile phones without internet access. The system will enable wallet top-ups, fuel pellet purchases, order tracking, and account management, supporting customers in Zambia, Malawi, and Mozambique.

1.1. Enable Accessible and Inclusive Transactions
• Provide mobile money integration for wallet top-ups and payments.
• Ensure real-time updates to wallet balances via USSD menus.
• Offer multi-language support for diverse customer bases.

1.2. Streamline Fuel Pellet Purchases and Order Scheduling
• Allow customers to purchase pellet bags directly via USSD.
• Provide automated order scheduling for future deliveries.
• Implement order tracking mechanisms for real-time status updates.

1.3. Ensure Secure and Reliable Transactions
• Require PIN-based authentication for wallet transactions.
• Enforce USSD encryption standards to protect customer data.
• Maintain 99.9% uptime with failover mechanisms for service continuity.

1.4. Integrate Seamlessly with Existing Systems
• Synchronize customer wallets and transactions with the ERP system.
• Ensure API integration with payment gateways and mobile money services.
• Connect to the Delivery Management System for order fulfillment.

1.5. Enhance Customer Engagement and Experience
• Enable real-time balance inquiries for wallets and tokens.
• Provide USSD notifications for order status updates.
• Develop loyalty tracking features for customer rewards and incentives.

1.6. Support Scalability and Compliance
• Design a scalable system to handle increased traffic during peak periods.
• Ensure compliance with Zambia’s Data Protection Act (2021) and ISO 27001 security standards.
• Allow for future expansion to new markets with localized adaptations.

⸻

2. Current Challenges

2.1. Limited Access to Digital Services
• Many customers lack smartphones or internet connectivity, restricting their ability to use mobile apps.
• Manual order placements via call centers increase delays and costs.

2.2. Inefficient Payment and Wallet Management
• No real-time wallet synchronization with mobile money services.
• Customers experience delays in order confirmation due to payment verification issues.
• No automated balance updates, requiring manual reconciliations.

2.3. Lack of Transparency in Order Tracking
• Customers cannot track their orders once placed.
• No self-service options to modify or cancel orders.

2.4. Security and Fraud Risks
• Need for strong authentication (PIN-based transactions) to prevent unauthorized access.
• Mobile money fraud risks require secure API integrations with mobile payment providers.

⸻

3. Key System Enhancements & Proposed Solutions

3.1. Seamless Wallet and Payment Integration

Proposed Enhancements:
• Mobile money top-ups with real-time balance updates.
• ERP-linked wallet synchronization for order payments.
• Multi-currency support, allowing transactions in Zambia Kwacha, SupaMoto Token, and Carbon Credits.
• Transaction history feature for customers to view recent payments.

3.2. Simplified Order Placement & Scheduling

Proposed Enhancements:
• Pellet bag purchase options (5KG, 20KG, 30KG, 50KG) via USSD menu.
• Automated order scheduling, allowing customers to pre-select delivery dates.
• Real-time order tracking, displaying pending, dispatched, or completed orders.
• Order modification restrictions (e.g., no modifications after 10 minutes).

3.3. Secure and Efficient Customer Transactions

Proposed Enhancements:
• PIN-based authentication for purchases and wallet management.
• Encrypted USSD transactions to safeguard customer data.
• Failover mechanisms for 99.9% uptime and service reliability.

3.4. Enhanced Customer Experience & Notifications

Proposed Enhancements:
• SMS notifications for order confirmations, dispatches, and deliveries.
• Multi-language support (English, Bemba, Nyanja) for better accessibility.
• Loyalty program tracking to reward frequent customers.
• Customer feedback surveys via USSD to improve service quality.

3.5. System Scalability & Compliance

Proposed Enhancements:
• Scalable architecture to accommodate growing user bases.
• Regulatory compliance with Zambia’s Data Protection Act (2021) and ISO 27001 standards.
• Automated performance monitoring to prevent service disruptions.

⸻

4. Next Steps

4.1. Short-Term (Next 2-4 Weeks)
• Develop a USSD menu prototype with core functionalities.
• Conduct API testing with ERP and payment providers.
• Define quick wins for early system deployment.

4.2. Medium-Term (1-3 Months)
• Implement full wallet synchronization and mobile money integrations.
• Conduct pilot testing with customers in Zambia.
• Develop order tracking and scheduling features.

4.3. Long-Term (3-6 Months)
• Scale the system to Malawi and Mozambique.
• Integrate AI-driven analytics to improve customer behavior insights.
• Expand functionalities to support new product categories and service models.

⸻

5. Conclusion

The USSD Customer Interface Project will bridge the digital divide, ensuring that all customers—regardless of smartphone access—can engage seamlessly with SupaMoto services. By integrating wallet management, pellet purchases, order tracking, and mobile money transactions, the system will enhance operational efficiency, reduce service bottlenecks, and provide a user-friendly self-service experience.

This document will serve as the foundation for the PRD (Product Requirements Document), defining the detailed specifications for development and implementation.

⸻

This updated high-level business goals document captures the strategic objectives and functional requirements for the USSD Customer Interface Project. Let me know if you need refinements or additional insights! 🚀
