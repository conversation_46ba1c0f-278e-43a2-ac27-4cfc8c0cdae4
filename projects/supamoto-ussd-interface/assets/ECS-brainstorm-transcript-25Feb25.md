1:09:19 - <PERSON><PERSON><PERSON>
Have a nice day, everyone.

1:09:23 - <PERSON> | ixo
<PERSON>, are staying on here?

1:09:24 - <PERSON><PERSON> (ixo)
Yeah, let's just stay on here.

1:09:26 - <PERSON>, yeah, I'm just looking for that.

1:09:31 - <PERSON>, can just get a quick thumbs up or thumbs down? Would this be valuable for me to be a part of?

1:09:39 - <PERSON><PERSON> (ixo)
So the intention of this session is to understand where do we want to fit in these three at a very high level? Where would they fit into our current infrastructure and architecture? So not necessarily, we're not deep diving into anything. It's literally 15 minutes and they will be done.

1:09:58 - <PERSON> | ixo
Right.

1:09:58 - <PERSON>
Thank you. We're going to drop off then.

1:10:00 - <PERSON> | ixo
Yeah, that's fine.

1:10:02 - <PERSON>
I'll listen in background.

1:10:05 - Shaun | ixo
Sure.

1:10:06 - <PERSON><PERSON> (ixo)
Sean, I don't know if you've seen the requirements, documents that ECS produced. So that's what they expected. Yeah, I know.

1:10:17 - <PERSON> | ixo
Do I maybe just pull them up if they're easily accessible? In a while, since I saw that. So just for a sort of broader context, these documents were created specifically for the U.S.
SCREEN SHARING: Alwyn started screen sharing - WATCH: https://fathom.video/share/bQMa9BECZNwLU5xsWU5cyZ8UfzE5Jkao?timestamp=4240.186375 ID grant and now that U.S. ID has been, has been rug pulled by Trump. We are now just developing this as part of the contract that we have with ECS. So there's an onboarding fee within the platform services agreement, which is $80,000, which they're transferring across to us by the end of this week. hopefully. And what I've agreed is that we're onboarding them over the next two, three months onto the additional kind of features of the platform. Now, the money that they're paying for that onboarding helps us to develop those features, but they're not all specifically for ECS. So as we run through these, context is that these are extensions to our core software capabilities that we will be providing to other cookstove companies and can actually use across a wide range of different use cases. And so we're building this in a way that this is our software, it's not ECS's software, and then they're getting it as a benefit of using the platform.

1:11:56 - Alwyn van Wyk (ixo)
Okay, great. And the way they, so the way they They're thinking has been sorry go ahead Sean.

1:12:03 - Shaun | ixo
No, so obviously we're doing it with their requirements in mind. So We'll want to have a set of PRVs for each of these things Where we've got the user stories and specific requirements and everything to really make sure it's hitting the nail of What ECS is needing?

1:12:24 - Alwyn van Wyk (ixo)
Yeah Okay, so they've got essentially got four High-level requirements of which these are the there's actually just three But the supermutter token itself and the tokenomics around this and how this can be used.

1:12:38 - Shaun | ixo
I think it's also Chunky enough to be broken up into it as a fourth requirement Then don't have you got that sense when you were with okay So I mean just for a bit more context there the Malawi It must contract which was signed last week with the Swiss government specifically includes $3.50 per ITMOS digital voucher for the households. And that can only be spent on fuel, but that system is needed. And so it's a very, very cool part of what we're doing. And there's a difference between the digital voucher and the on-chain payments for fuel that we're wanting to create. So I just want to make sure we understand the distinction between the two. So essentially, we're wanting to move away from using prospect, know, doing validation of the fuel purchase transactions, and all the validations will be on-chain. all fuel purchase transactions will happen directly on-chain.

1:13:57 - Alwyn van Wyk (ixo)
Cool. Okay, so just in terms of... of the scope itself, basically three major things, the customer management app, the delivery management system and the USB mechanism to communicate with customers with devices that will never be all very in the long term only be smart phones. just at a high level what I was thinking is the customer management app is actually just an extension of our survey JS forms and it's my gut feel is it slots in very nicely with what we already have on portal and can work in that space very easily. just need to take into the details obviously and make sure of that. In terms of the delivery management system that seems to be a different from kettle of fish and they need a lot more there and we want to use tracking all the way from warehouse to delivery all needs to be encapsulated in some way in a system and then the final part terms of ussd i don't think it's such a huge lift it's just a completely different technology that needs to be put in place for the communications and for customer engagement and interface weather systems and then obviously the token so all of these things are going to be deeply built into our system so the starting point I just want to be very clear the starting point is that these are platform capabilities that extend our platform rather than things we're building for ECS that interact with our platform so they just make sure that our thinking is in light from the right perspective which means that we heavily rely on our infrastructure

1:16:00 - Shaun | ixo
or matrix, et cetera, so that we are storing all data within the context of our platform, and we're using all of the tooling around that. So to support this, Peter and I went through portal and looked at all the sort of mission control functions. And I think we've got a pretty nice set of kind of features mapped out for that. And it's really nice because it still works within the context of the patterns that we have with sodas and so on as well as with oracles. So I think the main sort of complications in the system are really about how we interact with the legacy ECS system or allow for the legacy system to interact direct with our system because we need to provide all the API integrations for that, as well as with third-party services such as the Fiat on-ramping and off-ramping. So those are the things that are a bit unknown to us. For the rest of it, it's all just repeating patterns of what we already have.

1:17:21 - Alwyn van Wyk (ixo)
OK. This speaks to something that we wanted to do, which is to pull all of the data that we're currently pulling from SuperMoto's API to pull all of that into matrix. Michael, I think you were quite clear that that's going to be quite a big lift. So we need to be sure that we've got that nicely specified and in place as part of the big scope. OK.

1:17:44 - Shaun | ixo
Absolutely. OK. So just broad strokes. I can just talk through just kind of like the purpose of each of these things and what I have in mind. And then we can can go after this and start developing out those PRDs. So the customer management side of it, the first thing is onboarding. So it's, as you say, a server.js form that enables us to capture all of the data. There are some additional requirements now that come in under the new MADD. Much more information about sole baselines and things like that. But really those are just additional fields within the forms. Now we have a question about how to deploy this. Is it going to be in a sort of Jambo app or would it be within XOMobile? And my feeling is that we should be doing it in XOMobile. So we give us a solid application. The very nice to have but maybe not essential immediately is offline claims collection. So being able to fill in the form. forms with ART and internet connections, in case there's connectivity problems and you don't want agents not being able to collect the data. And I think that that's a general requirement that we have across many, many different use cases. In any case, and it's been part of the roadmap to build that into mobile. So it may require us just looking through some of the design flows, re-looking at how you look at my claim forms and things like that just to update that with what's now currently required and how we've changed things a bit. Now with the assistant and companion interface as well, so just thinking through those kinds of interactions. But fundamentally, you pull up a form, you fill in the form, you submit the form. Then on portal, there will be initially a manual interface which is basically claim evaluation, which is done by ECS staff members who for evaluation agents on that claim collection and in future we'll bring in more automated processing so that they can get to the hundreds of thousands of households without having to do manual reviews. With each claim that gets evaluated there'll then be a credential that gets issued, household credential and that is an important piece of data that feeds into the system including up to the National Registry and IMOB system so we need to just architect where exactly that data sits and how it gets accessed and so on because we want it to be within the context of an individual's matrix room so you know they've got their own data sovereignty but it also needs to be accessible to ECS and it also needs to be reportable into the National Registry system on that as well as as a

1:21:00 - Alwyn van Wyk (ixo)
similar to the way we use KYC credentials at the moment.

1:21:03 - Shaun | ixo
Yeah.

1:21:04 - Alwyn van Wyk (ixo)
Okay, so there's already an established pattern for that.

1:21:07 - Shaun | ixo
Yeah.

1:21:08 - Alwyn van Wyk (ixo)
Okay.

1:21:09 - Shaun | ixo
Okay, then the second user category is agents. So agents will use the mobile app. They will also get credentials and then they will be authorized to submit claims. First claim being the user onboarding KYC, if you like, claim that they would submit on behalf of the users. The second type of claim is a delivery claim. So when an agent goes out and delivers a bag, they need to claim that they did the delivery. And there would be an automated evaluation of that claim, which is done by an oracle that would ask for the one-time pin. So the oracle would need to be sort of long running and essentially get triggered. to generate the one-time PIN, which would go to the user's phone. They tell the agent the PIN number, and that does the handshake and validates that the delivery was done. As that delivery happens, the inventory moves across from the agent's distributor wallet to the household. We've got a full record of all the tokens that represent bags of pellets. These are the 1155 tokens that a household has, and we know exactly where those have come from, all the way from the factory, as you said, all the way through the distribution points to the end user. At the same time that that delivery gets evaluated and approved, the payment goes across from the customer's wallet into the receiving wallet, which is probably the supermodel's wallet. of the agent's wallet. So now you've got the data point that's needed to validate the claims. So in the same way as we do it with the fuel purchase claims, but now we're using the on-chain transaction as the basis and not the mobile money transaction. OK, so now you need a way for mobile money to get on-chain and off-chain. So there we're busy talking to the company that does mobile money integrations of blockchains. So we've signed the NDA with them and need to just talk through what the integration costs will be and also just there's some details around like who's custodian over it. There's also considerations around the USDC being the currency that gets loaded on-chain. So anyway, there's a a number of things we just need to work through there, both in terms of payment flows, but also regulatory stuff. But the idea is that a customer could load up their wallet using mobile money, and it would go into their blockchain account. USSD. So we need all USSD integration with that chain. So basically, the customer should be able to do most of the basic things that they can do with like a Jambo app, except it's abstracted away into USSD, which means we need to have a mechanism for custodial accounts as well as looking into how those accounts can be recovered and how they get claimed by someone. So if switches out their SIM card or loses their phone or something, how do they get any credits or actually money that's going to be on chain, that's belongs to them. a counter recovery mechanism as well. The USSD piece is actually pretty straightforward because it's already open source USSD libraries and actually I think Luke did deploy something before. So we need to look at what APIs that provides and then figure out which API integrations we need to build into the ECS system so that for instance they can message a customer or the customer can place some order that would work out all those flows and how much of that USSD integration would need to go into ECS versus it mostly being within our system and then ECS interacts with our system for instance as a bot in in our matrix room which can then trigger messages and transactions and things. And then in terms of the supermodo token that's sort of pretty much implicit in what I was just talking about, the Fiat on-ramp or Fram. The digital voucher scheme is different and they were proposing to just use the carbon tokens. So in the way that we prototyped already, of, that the household gets some carbon tokens into their wallet, except it would be done in a way that if they don't use those digital vouchers by the end of a certain period, they can be taken out of the wallet and used in the utmost transfer. And a lot of the stuff we've actually got kind of mapped out already, some of the flows, and so now it's a case of really sort of getting on into the details and taking the PRD approach to, like, figuring out exactly exactly what's needed, what's not needed. So we must really kind of keep things confined in terms of scope, certainly for the first phases and then we can go into like technical diagrams and then start figuring out how to do the implementation. The question around this is who's going to do all the work that's not called to what we're currently building and that's where I'm going to pause.

1:27:20 - Alwyn van Wyk (ixo)
I also want to play back to you what Matthias' expectation was from last year. He was very adamant that he wants to have daily stand-ups with a very specific developer who is dedicated to his work. I don't think he's said it in so many words but that's the gist of it. So I just wanted to get your sense of that and so he really wants to get bang for his buck and obviously we also focused on getting that right but there are many priorities so he wants to make sure that the other priorities do not override his. So meeting with him tomorrow morning after stand-up just to pick off now chicken. Just to set things up like his expectations to understand that again, just set a way of working with him, etc. So maybe just your thoughts on that.

1:28:13 - Shaun | ixo
So there's three parts to that. The first is that we need a customer success manager for exactly that role, so that on a daily basis, there's one focal person who can interact with whoever needs to be interacted with within ECS. But I'm going to get to that in a moment. And an overall just takes responsibility for this. Now it needs to be someone already in our team because we can't bring someone new and who doesn't understand the system and everything. So I think Alvain is going to be your responsibility.

1:29:00 - Alwyn van Wyk (ixo)
What a bit of mind for them, Eric.

1:29:01 - Shaun | ixo
Yeah, I think it's super exciting. The second part is that there's an outstanding question as to how much scope and work some it should be doing. And Matthias is very keen to phase them out very quickly. It's costing him a lot of money. And also, there's been some availability issues with the developer that he's been relying on. And there's very long cycles to getting anything done with them. So what I've asked for is that he gives us as much API documentation as they have available. So we can at least as a first step get a really good understanding of what can actually be interfaced with through APIs. Because we don't want to be going on the inside of their system. We probably will need to have a look at it. the system to understand data models and things like that. But fundamentally, we want to use existing APIs. And if there's not existing APIs, we do something in the interim, which is not building new APIs and building new capabilities in their system, but it's creating creative hacks for how we can still integrate without having to do that additional dev work. So there's a bit of sort of exploration and then coordination with a some bit of that stuff.

1:30:36 - Alwyn van Wyk (ixo)
Great. the third thing?

1:30:38 - Shaun | ixo
Third thing is there's a question about what in-country capacity we need to have. And should that capacity be embedded within ECS? Or should it be something that we as XR have? And I'm quite strongly feeling we need to have someone on the ground in Zambia. who's not necessarily a developer, but who can provide support and who's also presentable enough and knowledgeable enough to be able to go to meetings with stakeholders, you know, with the other companies, with Xemma, etc. So it's not always me flying out there. And can actually then also start working on the whole business opportunity space around around not just in cooking, but there's so many other things on the go that, you know, all great opportunities for us. Now, ECS does actually have a budget for a role. And so we're debating, you know, what would be the pros and cons of having someone employed by ECS versus employed by XO? And what should that role look like? What should their skills be, etc, etc. Now, another sort of thing to to factor into this is that Raymond has resigned.

1:32:06 - Alwyn van Wyk (ixo)
That's a blow.

1:32:08 - Shaun | ixo
Yeah, is it Raymond or Mushu? think it was Raymond.

1:32:13 - Alwyn van Wyk (ixo)
Well, I hope it's not Raymond, but I'll find out.

1:32:18 - Shaun | ixo
I think it's Raymond. Yeah, he was offered a job at another cookstove company with double the salary and everything. So they're trying to maybe get him back while they're looking at us as an opportunity to get someone who's even more kind of experienced and can be more senior. there will be someone who's got sufficient capability within ECS. And my strong feeling is that we should be employing someone, but then there's always the questions around who supervises them, who supports them in country and everything. So that's an open conversation. I really He said, I said to Jason, is this Jason, Ian?

1:33:08 - Alwyn van Wyk (ixo)
There's a Jason.

1:33:10 - Shaun | ixo
Yes, it's a guy or American. I was eager. And then we, we had call last week with him and Claire, was that their finance director. I said, look, let's give it four to six weeks so we can just work through understanding what's needed before going out and making any decisions about hiring new people. But that's the sort of timeframe within which we should be looking at. Yeah, like putting more capacity on the ground in Zambia.

1:33:46 - Alwyn van Wyk (ixo)
Okay. was just going to ask if you're in country, you don't mean Malawi too, you just mean Zambia. the second thing is Sean, I want to actually just recommend because I recommend that initially it's an ECS person, but being groomed and being trained and get it gotten ready to take on the role as exo world for exo world in other words it's like an aspirational goal because otherwise if they just jump in knowing nothing we're going to have to really spend a lot of time with them and also they then they've got this exaggerated sense of responsibility that they don't really deserve yet if to put it into in terms because they don't have the understanding yet.

1:34:35 - Shaun | ixo
So I don't know what would be the best thing let's go through this process of first understanding what we need and then make a decision of how to how to get that but I want us to be setting our our expectations very very high you know we want to have the best person available best person on the continent. who's eligible to be in Zambia, to be in that position. think of it as being someone who is a very key hire for XO. How we get there, let's see. But it will do us extremely well to have a very competent person on the ground who's able to do business development, also support and stakeholder engagement, et cetera. To have someone who's wearing the XO hat locally available will be a game changer for us.

1:35:39 - Alwyn van Wyk (ixo)
Yeah, okay. They're standing in your shoes though when they go to Zambia and all of these guys. Great. needle in the haystack, in my opinion. We'll find them. Okay. That's 10 to 31. So I'm happy that I've gotten out of this what I needed to. Just want to get Michael's quick view as well.

1:36:04 - Michael Pretorius
Yeah, not really any views, just planning, guess, especially stuff like the U.S.S.D. I mean, U.S.S.D itself is a tool, it's not all acquisition, but it's all in compasses, the centralized accounts, yeah, that stuff, yeah, you can imagine that was very tricky, especially if you guys see the hack, ethack from that, yeah, that kind of stuff. Let's just be clear on that properly.

1:36:38 - Shaun | ixo
Yeah, and my sense is that we need more developer resources, but without growing the team was that, you know, I really am committed to the world-class football team, and we're already sort of reaching the max of, you know, the available places in that team. So multi-asking, yes. Anyway, let's just give it a bit of thought. It is quite feasible that we might end up with a local company in Zambia, for instance, you know, that employs more than just one person. We can't sort of foresee how this is going to go, yeah, the core principle is, you know, let's build, let's build this with the core team and anything else we can do in collaboration with others and through multi-asking. And so, I mean, maybe just on that, for instance, the USSD integration, if we've got a really, really well-spec.prd. it'll serve us all well. It provides good software documentation for the documentation purpose. really enables us to know what we're building and why we're building it. And it also allows us to give very, very clear specifications to an external developer if we want to bring in an external developer. So I think this is an ideal opportunity for us to start doing the multi-asking and just finding really, really good developers. mean, a bunch of people have been working on things like USSD integration with blockchain. And I'm sure they would be absolutely delighted to get involved in something that's really going to have traction finally. And then as we start bringing in more of the AI oracle capabilities, that's not included in this scope, but there's a whole bunch of stuff that Mattias expects and that I've kind of promised him on the AI automations and, you know, labeling ECS to scale to a million households and now another 129,000 households in Malawi using software systems rather than large teams of employees.
