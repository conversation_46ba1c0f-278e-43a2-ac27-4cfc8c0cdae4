Here is a high-level business goals document based on the meeting transcript and Fathom.video summary. This document serves as the foundation for the Product Requirements Document (PRD) for the SupaMoto and IXO collaboration.

⸻

SupaMoto & IXO - High-Level Business Goals Document

Date: March 06, 2025
Stakeholders:
	•	SupaMoto (ECS) Team: <PERSON><PERSON>, Sales Reps, Call Center, Lead Generators
	•	IXO Team: <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>
	•	Key Business Areas: Lead Generation, Customer Onboarding, Lifecycle Management, Call Center Operations, Agile Development

⸻

1. Business Goals and Objectives

1.1. Improve the Efficiency of Customer Onboarding
	•	Reduce the time and complexity of onboarding new customers.
	•	Minimize the number of manual steps and handoffs in the onboarding process.
	•	Automate parts of KYC, GPS data collection, and customer verification.
	•	Increase monthly onboarding capacity to support business growth goals.

1.2. Optimize Lead Generator Role and Performance
	•	Define clear criteria for selecting lead generators and standardize their onboarding process.
	•	Improve tracking and incentives for lead generators.
	•	Ensure efficient distribution of pellets and potential stove sales.

1.3. Enhance Customer Lifecycle Management
	•	Implement a Portfolio at Risk (PAR) system to monitor customer payment behavior.
	•	Establish proactive measures for late-paying customers to prevent default.
	•	Streamline the offboarding process, including stove repossession and replacement.

1.4. Strengthen Call Center Operations and Support
	•	Improve customer verification and screening to accelerate onboarding.
	•	Enhance the call center’s bulk messaging and customer engagement strategies.
	•	Streamline activation and deactivation of IoT-enabled stoves.

1.5. Implement Agile Development Practices
	•	Establish structured, iterative development cycles (1-week sprints).
	•	Maintain daily stand-ups and weekly review sessions for rapid feedback.
	•	Ensure close collaboration between SupaMoto’s field team and IXO’s development team.

⸻

2. Current Challenges

2.1. Inefficiencies in Customer Onboarding
	•	Manual KYC data collection, requiring physical visits for GPS coordinates.
	•	Dependence on Solaris Pay Drops CRM, which slows down customer screening.
	•	Paper-based contract signing, adding administrative overhead.
	•	Call center agents manually verifying and approving customer applications.

2.2. Lead Generator Management Issues
	•	Inconsistent criteria for selecting and assessing lead generators.
	•	No clear digital tracking of lead generator performance.
	•	Limited role expansion beyond pellet distribution.

2.3. Portfolio at Risk (PAR) and Payment Challenges
	•	Need to better classify customers based on their payment status.
	•	Customers who fail to buy pellets for 30-60 days require constant nudging.
	•	Repossession of stoves for non-performing customers needs a structured legal process.

2.4. Call Center Bottlenecks
	•	Call center responsible for customer verification, activation, and follow-ups.
	•	Heavy reliance on manual calls and bulk SMS for payment reminders.
	•	Limited real-time insights into customer payment behavior and stove usage.

⸻

3. Key System Enhancements & Proposed Solutions

3.1. Streamlined Customer Onboarding Process

Proposed Enhancements:
	•	Implement digital KYC verification (e.g., mobile form submission).
	•	Introduce automated GPS data collection to reduce manual visits.
	•	Enable e-signatures for contracts to eliminate paperwork.
	•	Automate CRM interactions and integrate AI-powered chatbots for initial screening.

3.2. Improved Lead Generator Role & Tracking

Proposed Enhancements:
	•	Standardize lead generator onboarding criteria.
	•	Introduce a mobile dashboard for lead generators to track commissions and stock.
	•	Explore potential expansion of stove distribution via lead generators.

3.3. Enhancing Customer Lifecycle Management

Proposed Enhancements:
	•	Implement automated reminders and escalation workflows for late payments.
	•	Develop a risk-scoring model to categorize customers based on PAR status.
	•	Improve transparency in stove repossession and replacement policies.

3.4. Optimized Call Center Operations

Proposed Enhancements:
	•	Automate bulk SMS reminders and customer payment nudging workflows.
	•	Integrate self-service options for customers to check payment status.
	•	Improve IoT integration for seamless activation and deactivation of stoves.

3.5. Agile Development & Collaboration

Proposed Enhancements:
	•	Follow 1-week sprint cycles with a focus on delivering quick wins.
	•	Establish clear roles:
	•	Bupe: Primary business contact and field team liaison.
	•	Alwyn & IXO Team: System development, architecture, and integrations.
	•	Graeme: Data product lead, ensuring smooth transition of business needs into technical solutions.
	•	Conduct weekly review sessions to prioritize development efforts based on field feedback.

⸻

4. Next Steps

4.1. Short-Term (Next 2-4 Weeks)
	•	Develop a flow diagram mapping the onboarding and customer lifecycle processes.
	•	Identify the top 3 quick wins to improve onboarding speed and efficiency.
	•	Initiate test integrations with Solaris Pay Drops CRM for automation opportunities.

4.2. Medium-Term (1-3 Months)
	•	Implement automated KYC and GPS verification tools.
	•	Develop an MVP version of a lead generator dashboard.
	•	Improve customer screening and risk assessment models.

4.3. Long-Term (3-6 Months)
	•	Scale the system to support higher volumes of customer onboarding.
	•	Optimize stove activation and payment tracking with IoT improvements.
	•	Develop a fully automated customer onboarding workflow.

⸻

5. Conclusion

This document outlines the high-level business objectives for the collaboration between SupaMoto and IXO. By addressing key inefficiencies in customer onboarding, lead generator management, customer lifecycle tracking, and call center operations, the system can significantly enhance scalability and efficiency.

The next steps will involve refining these business goals into a formal Product Requirements Document (PRD), which will define system specifications, integrations, and feature implementations.

⸻

This high-level document serves as a foundation for the PRD. Let me know if you need refinements or additional details! 🚀