SupaMoto / IXO - March 06
VIEW RECORDING - 63 mins (No highlights): https://fathom.video/share/zBVcrbybnYzaBfLuxU6VSfrLz2W8yzYJ

---

0:18 - <PERSON><PERSON> (supamoto.global)
  Hi, unfortunately, my network is quiet, so the moment I allow video, everything just goes really, really wrong.

0:28 - <PERSON><PERSON> (ixo)
  So, unfortunately, I was going to suggest that I'm going to do the same on networks, also a bit wonky if there's too much video and everything going right.  So, let's just agree. Hello, and video off. Yeah, so I just wanted to talk you through how I think we can do this session to make the most of our time together today.  Maybe <PERSON><PERSON><PERSON>, off the cuff, straight away. what are the most burning questions that you have on your mind and we're not going to answer them straight away but in core messages on the side here I'm going to write the doubt.  So let's start with your most burning questions. Okay thank you.

1:17 - <PERSON><PERSON> (supamoto.global)
  So I think most of my questions would revolve around how basically the dynamics of the project are going to work, the specific roles that each individual covers and another one is just regarding well some beats interaction within development as well because I'd like to understand the extent to which they can let's say change or manipulate something within the system because as you are the admin banner was relatively new so we do have a few bugs here and there and  So, a few minor requests for adjustments in the case of user functionality and all that. So, I just wanted to ask those two questions in general.

2:14 - <PERSON><PERSON> van <PERSON>yk (ixo)
  Okay, cool. I've just written it down as I heard you saying it now. Just let me know if I can change it.  Go ahead.

2:23 - <PERSON>upe <PERSON>laba (supamoto.global)
  Can you see what I've written? Yes, that seems accurate enough, yeah. Okay, great.

2:31 - <PERSON><PERSON> van <PERSON>yk (ixo)
  So, let's speak to those quickly. In terms of the dynamics of the project, moment we kick off in, that's partly why we are meeting today, is so that we can kick off properly and get things really, get the ball rolling properly.  We've met with Sasha yesterday. Sorry, the day before yesterday. I actually can't even remember. I think it was yesterday.  And it's amazing what he used today. And so, from my point of view, And just what I'm hearing from Matias is that you will be our contact person for all things related to the business requirements and what the system should be doing.  You've got access to the whole team on the administrative side, as well as the team in the field alongside Tawela.  So you are our go-to person for anything and any questions related to the product, to the whole project. Is that correct?

3:33 - Bupe Kalaba (supamoto.global)
  Yes, yes, that is 100%.

3:36 - Alwyn van Wyk (ixo)
  Perfect. So you are our most important person and that puts you in the fantastic position that we want to speak to you every single day.  So it won't be for hours on end, it will just be 15 minutes every day, what we call an agile stand-up.  You might also be, just stop me if you know all of this stuff and you've worked in this way before.  It'll be an agile stand-up on a daily basis, 15 minutes. And what we work towards is, first, we go into these stand-ups, we will speak about what is the first thing that we want to build?  What's the highest priority piece of functionality that we want to build? And then we go into a session where we analyze that, we design it together as a collaborative force, and we come up with the tasks and we plan for it.  And then when we meet every day for the 15 minutes, we just check in to see, are we still on track for what we planned to deliver in a certain time period?  Now, what I want to suggest initially is that we have our time period. We set those to one week at a time.  Number one, what that forces us to do is to think very clearly about quick wins and things that are really the highest priority, and not work for months on something  that's maybe not even the highest priority. So it just gets us into that habit. We can always change that to a two-week cycle or even a monthly cycle but for now I think we should just make it as short and as quickly as possible.  Okay so what happens during this week in my opinion is that on a Monday we understand what our week plan for the coming week we carve out a small section of functionality and feature that we want to deliver and actually showcase and have fully functional by the Friday.  And on the Friday, Friday afternoon I suggest is that we then meet as a team, we review how far we came as a team, see if that is what our intent was and whether that's good enough for us then to take into the field the week after.  So by Friday I would suggest that at one o'clock we meet and we have an hour long session just to review what we're  what we have at the moment, what's the concrete something that we have. And then we, from between 2 and 4, 2 and 5 that day, we find a mechanism, a way for that to be in the hands of yourself, Tawella, your admin crew, your crew in the field and for them to actually work with it from the Monday onwards or from the Saturday onwards so that they can give us immediate feedback in terms of does this work for them or does it not work for them.  Okay, I mentioned a lot of things now and it's probably quite daunting and it just sounds like your first question is probably, Alvin, you're insane.  How do we do that? There's just too much and what I can guarantee you and assure you is that we all make it small enough pieces of work so that we can fit it into a week.  Nothing that's the key. Any questions?

7:02 - Bupe Kalaba (supamoto.global)
  Yes, so just regarding feedback from, let's say, the field team, the sales team and all, is there a particular format you would like that in, would it be like a check list that you would like ticked off or is it just one functionality or one or two functionalities they review it, pick out its functionings, how it works within real time in the field and then give you that information as raw as possible just there, sorry, that was a bit all over the place.  No, no, that's perfect.

7:40 - Alwyn van Wyk (ixo)
  You said the key words I was hoping you would say is as raw as possible. So, okay, even if it's a video clip where they record their phone as they're working on it or maybe they have an interaction with someone or they are on a web application on the browser then record that and just talk about it.  If you want to talk in the, I don't know what the native languages are in your, in Zambia and everywhere, if English aren't their first language, then let them speak in whoever, speak in their own language.  And then we will find a way to translate that and understand that. Okay.

8:17 - Bupe Kalaba (supamoto.global)
  All right.

8:18 - Alwyn van Wyk (ixo)
  That's clear. Just on that, on that point, we've got so many tools these days with AI assistance and things that the more raw it is, the better.  Okay. That's fantastic.

8:33 - Bupe Kalaba (supamoto.global)
  My other question is just for you to just recap on the meeting times. that was throughout the week, it was just Monday and then Friday.  Sorry, I kind of missed that point.

8:46 - Alwyn van Wyk (ixo)
  So in terms of the longer sessions, and I think we should set them up for one hour, but if it's 15 minutes only, that's fine.  It doesn't matter. Those ones will be on a Monday and a Friday. It's a Monday morning as early as we can after we've looked after the rest of our responsibilities.  And on a Friday as late as possible, but without missing that session. So it gives us enough time. Then after we've done the Monday, we now know what our planning is for the week going in.  And all we need to do is on a daily basis, Tuesday, Wednesday, Thursday, we will meet for 15 minutes as a team.  it's five minutes, that's also fine. all we say is what was I able to achieve yesterday? What do I hope to achieve today?  And do I have any blockers or impediments standing in my way to achieve that today? Which then allows everybody else.  So you'll have a few, like in terms of the roles that's your second point. The roles are the actual doers, people building things, making sure that they work, and people who are not doing that.  So those people who are doing and fixing and creating and building, those are the ones who speak on a Tuesday, Wednesday and Thursday and they can then ask the rest who's not doing that, who can answer questions.  They then ask them on a Tuesday, Wednesday and Thursday for clarity or a way to unblock their progress, things like that.

10:36 - Bupe Kalaba (supamoto.global)
  Okay. All right. Now that's great.

10:40 - Alwyn van Wyk (ixo)
  That makes sense.

10:41 - Bupe Kalaba (supamoto.global)
  Yeah, that makes a lot of sense. Thank you. Awesome. Right.

10:45 - Alwyn van Wyk (ixo)
  And the last thing in terms of how to be handled bugs, in exactly the same way as we handle normal things.  So we, that brings me back to the roles currently. An extra point of view, I am your main point of contact.  If anything happens along the way that you're not happy with or that you are happy with or that needs clarification or we need to make a change, whatever, then please make sure I know.  Because then I can make the necessary things happen on our side and liaise with your team and with Sasha and with everybody that needs to be liaised with.  Cool, so that's point of contact. And for me, you are that person too on the supermoto side. Okay, okay.  Is that right? yes.

11:39 - Bupe Kalaba (supamoto.global)
  I know that is right. Maybe there will be a bit of interaction between myself and the team here, but point of contact will be myself.  Cool.

11:51 - Alwyn van Wyk (ixo)
  So if ever you may have any questions about maybe all in terms of the project, where EXO is in terms of  of deliverables etc please ask me and you can ask it straight in the slack channel but always just tag me so that I can respond and know about your question.  I'll do the same if there's something that's unclear from the supermodel point of view. then in terms of the phases that we're going through so this first phase is discovery so we try to understand as much about your requirements or data structures, your data model, the APIs in place, the existing applications that you have in place and you need to be able to create a design around this.  So it's not yet a UI or a UX design. UI means use interface, UX means use experience. It's not yet that it's an architectural design.  We're trying to understand where we fit all of these technology pieces together so that we can build the the most efficient and secure and performant system possible.  So during this first phase, Graeme is key in that role because he's our data product's lead and he focuses a lot on that.  He needs to also understand a lot about the requirements to be able to map and translate what SASH is giving us in terms of the data model and the system to be able to map all of that properly.  I'm supporting in that space by creating the product requirements document, creating architectural diagrams and architecture documents and putting it all together.  And I will liaise with Michael on our side who is our CTO and chief architect for the whole system.  What is with Sean Conway, whom I think you know quite well. and with Matthias as well. with you. So that's that first phase in our discovery.  And then because we've got to do a big chunk of discovery initially, maybe that's just what we're going to be focused on next week, as well as as as bugs.  And we're going to pull Sasha into those sessions. So we kick off our first print on Monday. it will be, we will take on whatever we can take on as much as we understand within one hour on the Monday, where that is what we will focus on and get something tangible out by the Friday.  Cool. So just to give you some level of comfort, it usually takes about three weeks for a new team like us.  So what I mean by a new team is like Pupe, Alvin, Graham, Sasha, Tawella, anybody else you want to pull in a team like that.  It takes about three weeks to find our rhythm. That rhythm means that we understand how much to take on at the beginning on a Monday.  And we understand how to interact and work together and what our constraints are. And it takes us about three cycles essentially before we understand our inner workings and then we can start detecting quite nicely.  Then we can say, okay, we bite off so much on a Monday. Cool, we know we can deliver this as a team.  We just got a gut feel. And on a Friday we see, yep, that's what we did. And then we start accelerating in terms of that as well.  Because we now get more efficient, get better understanding between each other becomes better. And we start overachieving, which is a great sign.  Okay, all right, that is 100% clear. Okay. The other thing I want to make really super obvious is that that's the way we work at XO as well.  There's no such thing as a failure. What we're all trying to do is just learn from what we build.  If we want to call it a fail, then the only thing we got out of it is to learn something.  So we're continuously learning. We all know that we're trying our best. So it's just a very positive environment in a very, very constructive way and creative way of building things.  So that's the way that's how we approach it.

16:38 - Bupe Kalaba (supamoto.global)
  Okay, that's a very powerful approach towards work. think I might adopt that in the future. Awesome.

16:49 - Alwyn van Wyk (ixo)
  Okay. All right. maybe Graham, just from your point of view, what did I miss and what should we be talking about in terms of these three?  questions?

17:05 - Graeme Leighton
  I think just in terms of the dynamics of the project, so the way it works is that we have people in different time zones, so there might be slack messages flying around at all times of night, depending on where you are in the world.  There's no expectation to work on something outside your work hours or when you're not working. No one gets out of bed to respond to slack messages when they're sleeping, that kind of thing, so just keep that in mind.  And then as we get this team together and we kick off with the first sprint, there's going to be a lot of focus on prioritization and what is the minimum we need to test an idea.  So we can iterate on it and improve it. And also around priorities. This is a significant project, so starting with something that we can achieve as a quick win together to help us get through something sort of end to end, that we can produce, get out there, look at it, reflect on how we work together.  That's always a good place to start. Okay.

18:35 - Alwyn van Wyk (ixo)
  Okay, that's great.

18:37 - Bupe Kalaba (supamoto.global)
  Thank you. Okay, you can...

18:41 - Alwyn van Wyk (ixo)
  Before we move on, just want to underline that when Graeme says the India that those small wins, those successes, that's what drives us as a team.  And that's what keeps our energy up and makes us really positive and keeps us going. So that's very well said, Graeme.  Thanks.

19:01 - Graeme Leighton
  Yeah, and we are sorry just to add to that if we we try something and we find it doesn't work well is there's no shame in changing changing what we're doing and how we're doing it changing the approach that that idea of failing forward iterating quickly is is something that's deeply embedded in our team cool Is there anything else around How are going to be sitting out the project is set up?

19:33 - Alwyn van Wyk (ixo)
  How are we going to be running at rolls and how we'll handle buggy bugs from your interview?

19:41 - Bupe Kalaba (supamoto.global)
  Uh, yes, um, I just okay. Um Currently we're to adopt the current flow that you have Uh, we haven't been actively working in development on the admin panel in some time and Since we're back.  This is the biggest project. I think and I'll play it. So I have no queries or issues with how you've explained the dynamics, how things will be moving.  Again, this is just from hearing everything, but I think more feedback or more information would be given as we move along within the project itself.

20:25 - Alwyn van Wyk (ixo)
  Groovy. Okay, I'm going to share my screen because the one part that we need to properly understand is whether, just let me know if you can see my screen.  Yes, in the screen. We need to understand whether these requirements documents are still valid or not and how much of it is still valid.  So I'm going to bring them up and then I think a good way for us to just assess whether  You and Raymond are on the same page and whether there was enough time for you guys to actually Sit down and work through what Raymond is created.  Whether that is there or not is maybe just. I'm going to ask questions around us and just explain it to the best of your.  Of your abilities or as much of you as you understand of it and then whatever we pick up as something that.  That's a we don't there's a gap that we need to go and explore a bit more then maybe we can take that away and you can.  Have a conversation with Raymond or just make sure in your own mind that you understand what these things mean.  And also keep in mind that this is a good starting point. This is not necessarily the best thing for us to do right.  It's just a way for us to understand things better. And we can adapt this and change this right. So maybe my first question was, what exactly is a lead generator and how does how do they work?  does it mean to be a lead generator? Okay.

22:13 - Bupe Kalaba (supamoto.global)
  Okay, so just to bring to light us the first point Raymond is no longer with SuperMoto. at this point in time, I'll be filling in his role until further information is given.  So I just wanted to bring that to light in case you are not aware. Can I go into the explanation or maybe you have any questions regarding that or the documents in general?  I'm happy.

22:47 - Alwyn van Wyk (ixo)
  Matias did give us a heads up that Raymond's no longer there. Okay. right.

22:54 - Bupe Kalaba (supamoto.global)
  So moving into the definition of lead generator. this just in a straightforward term. a lead generator is basically someone who generates leads.  let's think of them as an external individual who is not affiliated with SuperMoto. That is at least holds, I can say, considerable amount of influence within their community.  So think of small township. Everyone knows everyone, but there's one particular person who is able to interact, communicate, talk to most of the people in their community to communicate on SuperMoto's behalf on particular things.  in the case of lead generators, what they do is that at least the way they currently function is that they are assigned consignment stroke and they distribute it amongst the different or the variety of customers that are within their community that own a SuperMoto stroke.  So let's say that we provide them with, let's say, 100 bags of pellets. And then their main role is to distribute this to the different people in the community that have made payments to SuperMoto and have been confirmed that the payments have come through.  So that's one role of a lead generator. basically a distribution. They act as a distribution point. And from that particular role as well, they're also provided in upon distributing each and every bag.  So if they distribute a 50KG over a period of time, like 50KG bags, they'll be paid in commission, a small amount that is calculated at the end of the month.  So that's how lead generators function in their first row. Their second role is to bring in new clientele, possible leads for the business.  if there's someone in their community, they feel they have community. with that needs a stove or are interested in the stove, they would bring that through to SuperMoto to bring that information in where they're located, what their names are, basic general information.  And then from that point on SuperMoto would then try to engage with our customers, see if they meet the criteria for onboarding and also just let them see if the information is accurate.  And then from there, they would be onboarded as a SuperMoto client within the community and be able to pick up the different bag sizes from their lead generator as a collection point.  Both of those roles involve committing allocation to the lead generator for their efforts. And to my understanding, there is a possibility in the future that lead generators will be, at least there'll be a bit more stricter measures to the customers that the lead generator brings.  the good customer, the bad customer, and that would then influence the commission that is given to the generator. Hopefully I didn't lose you at any point or say too much.  No, that's brilliant.

26:12 - Alwyn van Wyk (ixo)
  Quick question. Do you call your, the customers, do you call them customer or client?

26:20 - Bupe Kalaba (supamoto.global)
  The term is, both terms are used, but customer and client is, yeah, I don't think we have any issues with that.  use both of those terms. Okay, so I think we can maybe settle on one.

26:35 - Alwyn van Wyk (ixo)
  That'll be great. Okay. That's number one. The second thing is, do the lead generators also distribute stoves or only pellets?

26:46 - Bupe Kalaba (supamoto.global)
  At the current moment, they only distribute pellets, but there is a possibility for them to distribute stoves, but again, that depends on the efforts of  application itself so right now it's just pellets. Is there anything else that they distribute? Yes so in the case that deliveries for example for small items like accessories or accessories that are related to the stove are needed it's a bit more efficient to drop that off or to hand that through to the lead generator to then pass on to the customer in question so if a customer buys a charger and it's easier to just when we are allocating consignments to the lead generator just drop off the charger to them they then proceed to also passing on that to the customer in this case.

27:48 - Alwyn van Wyk (ixo)
  Great and do you have a full list of all of those accessories or things that could be all they bought by the customer or just passed through?

28:00 - Bupe Kalaba (supamoto.global)
  So they are bought by the customer so all transactions through the lead generate have to be confirmed with payments that come in.  So someone just doesn't have the ability to just collect it back from a lead generator. So they have to confirm this customer make a payment within the system through contacting our call center.  It's a bit of a process but usually it's done through the call center. So the call center confirms that this customer did make that payment and the bug can be.  It proceeds to providing the bag or item to that particular customer upon pick up. Okay, great.

28:39 - Alwyn van Wyk (ixo)
  And just to the first question, do you have a list of these items that other than pellets or cook stores that can be bought by the customer?

28:50 - Bupe Kalaba (supamoto.global)
  Yes, we do have a list of all items that can be bought by the customer. Okay, great.

28:56 - Alwyn van Wyk (ixo)
  So maybe I think if at some stage you can just pass that through to us. Then the second thing is, in terms of these lead generators, do they also have to repackage the pellets?  Do they get it in bulk, or do they get it in the denominations that they require? They receive them in the denominations.

29:19 - Bupe Kalaba (supamoto.global)
  So that could be of the different bag sizes, but it's not into them in bulk for them to repackage.  OK.

29:28 - Alwyn van Wyk (ixo)
  And do you have a list of all of those different bag sizes as well?

29:32 - Bupe Kalaba (supamoto.global)
  Yes, yes, we do. Awesome.

29:35 - Alwyn van Wyk (ixo)
  OK, then another question before I hand over to Graham. What sort of, how do you measure whether a lead generator has now a big enough area, or how do you measure that?  So in other words, if lead generator A is an area where it's very rural, there are people, people are not

30:00 - Bupe Kalaba (supamoto.global)
  close they're not densely populated so they live far far far between each other versus someone who lives in a city and they they've got like within a very short distance they've got hundreds of people the customers that they can service how do you determine whether somebody has enough customers now or not okay so currently as it stands the mapping of lead generators is it's not a full proof mapping but it kind of works at the current moment so the lead generators and this is how basically the distribution works is that lead generators are at least onboarded so let me just use that the lead generators are at least onboarded as a supermoto distributor when they meet a specific criteria of having enough storage space so this is an assignment or an assessment that's done by  sales team beforehand so they visit the lead generator confirm the the location that this lead generator will be using as a point of reference and then assess the amount of storage they have they there's also a preliminary assessment of how many customers live in that general area so the aim is to have at least the lead generator be able to provide stock for customers within a one kilometer radius so that's a that's at least what we have set to be the walkable distance to collect a bag from the customer's home to the lead generator so a one kilometer radius so usually those are the other main points that are taken into account as well as ability to generate leads because we have to confirm that and see if this lead generator is viable so those are the main points that are used so there is confirmation  storage space, a number of customers living in the general area that lead generators in. Usually this is just done using just rudimentary tools.  It's a Google Maps or QGIS just for mapping and then from there we're able to make the assessment and say this point would be the best to have a lead generator and then proceed to trying to locate someone in that area who fits the specific checklist.  Did I answer your question? Yeah that's brilliant.

32:30 - Alwyn van Wyk (ixo)
  Thank you and in terms of so there seems to be an onboarding process by the sales team of lead generators.  Yes okay great and does lead generator have to own a cook stove as well?

32:45 - Bupe Kalaba (supamoto.global)
  I would like to say yes because it's easier for someone in the community to give advice on a particular product if they own the product themselves.  So I can say 90, 94% of the generators do on stoves of their own. Should be all of them, but I will need to confirm on that.  But I'm almost 100% sure they do on stoves. Okay, great.

33:12 - Alwyn van Wyk (ixo)
  All right, thanks. That was brilliant. I'll just add lot of my questions. Graham, over to you. Can I add in here?

33:20 - Graeme Leighton
  When you are identifying and onboarding new generators, you've spoken a bit about this sort of network in the community and requirements around storage space.  Could you give us, are there any sort of requirements that are checked around sort of their level of technical literacy or certain devices that they would have or internet access or those kinds of requirements that you use?  And then if you can all just tell us about how much of their time are they putting towards these activities?  on maybe a weekly basis.

34:03 - Bupe Kalaba (supamoto.global)
  OK, with regards to the literacy, we would at least prefer them to have a basic knowledge of just communication tools or applications like WhatsApp, because WhatsApp and just basic typing and formatting because with an individual who referred to as a reset and lead generator representative from SuperMoto who is in charge of interacting with them.  their communication is based off that. They're supposed to be able to at least provide detailed reports on their stroke, how many bags they have left, how many they have distributed, the names and all information of particular customers when they come through to collect.  That's for the sake of reconciliation. for gadgets or devices that they need to have. I'm not sure I'd have to confirm on their agreement but most of them if not all should have access to basic smartphone that again can install those applications to force that easier communication.  With regards to language they can definitely to express themselves in whatever language they are comfortable in so that the information doesn't lose any value in short.

35:37 - Graeme Leighton
  Did I answer your questions? Yes, it's good to just understand sort of what is the what's currently happening and what's the current sort of baseline activities and then in terms of the time they're putting towards this it's a sort of full-time one or two hours a day how how active are they in the

36:01 - Bupe Kalaba (supamoto.global)
  Okay, so active, if I'm not mistaken, through the agreement, they should be working at least within the bounds of normal working hours here.  So that should be, let's say, maybe 8 or 9, starting 8 or 9 a.m. and then stop operating or distributing actively by 5 p.m.  in the evening. Reason being is that the verification process doesn't move the call center and also some forms of communication are directed towards their POC here at Supermoto and these people do clock out around 7 p.m.  So that's Monday to Friday. They, for some time in the morning to about 3 or, sorry, not 3 or 4, should be 2 or 1 p.m.  in the afternoon. So I could say an average of maybe 8 or an nine hours a day during the week and then on the weekends, Saturday only, should be a good four or five hours Sunday.  They might be distributing but we like to discourage that seeing that as there is no confirmation between the lead generator, the customer who's made the payment and consent since consent isn't active during those hours.

37:27 - Graeme Leighton
  Okay, thank you. And then I think the last question from my side is how many households would a lead generator be engaging with?  sort of roughly. We're talking maybe 20 households per lead and generator will up to 200. What's that sort of range?

37:52 - Bupe Kalaba (supamoto.global)
  That's greatly dependent on the storage space as well because the storage space influences many households are able to cater.  So if they have a reasonable sized storage space that can hold, let's say, 20, 50 bags at once, they're able to cater for, again, just assuming of the number of bags, 20 to 50 customers.  Well, that's given the assumption that it's a bag per customer. So within that range. So, again, greatly depends on the storage space they have.  I could, if you need the extra information, just try to query the sales team and get you and see if they have the metrics on specific storage space or how many bags, if we're going by that, you need to measurement and regenerate the whole thing.  Okay, thank you.

38:54 - Graeme Leighton
  I think this context is really useful, especially for our team we have a sense of who and how many people will be actively using what is being built.  I think many of us are quite quite removed from the reality. So this kind of context really helps focus us and understand things like if there is a bug in something and just sort of what is the magnitude of impact on the ground.  Okay, all right, that's good.

39:29 - Bupe Kalaba (supamoto.global)
  Just if you any questions regarding the lead generators, so I can also just query the sales team if it's something that they are aware of or maybe it's something that they could have that you might need.

39:44 - Alwyn van Wyk (ixo)
  All right, I've got a few more. So just in terms of these internal roles, you mentioned now a POC that the lead generator speaks to, is that some, how does it fit into these roles for your internal team?

40:01 - Bupe Kalaba (supamoto.global)
  Okay, so just give me a minute, so internal roles include admin, I think, and there's sales rep, retail assistants, stock office, stock manager, and dispatch manager.  I would say they would fall within the sales rep role. It's not specified there, but they would fall within the sales rep role, seeing as they are the main point of contact between supermoto and lead generator, so I would say sales representative.

40:36 - Alwyn van Wyk (ixo)
  Alright, so POC means point of contact in your context.

40:41 - Bupe Kalaba (supamoto.global)
  Okay, good stuff.

40:43 - Alwyn van Wyk (ixo)
  Then I want to ask, alright, so we are trying to get a high level view now, but we actually dove very deep into lead, into the lead generator role now, which is great because it gives us a nice perspective of how it all fits together.  context. If you had to choose the biggest pain point for your team right now, what is that biggest pain point that you would want to have solved immediately?

41:17 - Bupe Kalaba (supamoto.global)
  I would say for our team, do you mean the FinTech team or just the SuperMoto team in general? SuperMoto in general.

41:30 - Alwyn van Wyk (ixo)
  Okay.

41:31 - Bupe Kalaba (supamoto.global)
  I think the biggest point we would like or the biggest issue we would like to solve right now is the level of efficiency and time it takes to onboard a customer.  This is regarding the onboarding app of course. So currently we are using Solaris' pay drops as our CRM. That's to basically onboard the customer, register them, manage devices to some degree.  as well as profiles and statuses of a long and long-winded process to onboard one customer. And I think I'll share the steps for onboarding a customer with you.  Currently, there are several steps that are involved with onboarding a customer, and I'm assuming also Toella would highlight this in our future meetings.  But there is, again, lead generation, so quite right we have to generate the lead. We would have to review the information there, circle back to the customer if they fit specific criteria, and then after that, there would be the collection of the customer's information, the analysis or review of the customer position, making sure everything is correct, and in order, collection of GPS coordinates, which is also a very long process because we would have to physically go to the customer's location and get those GPS coordinates.  And then after that, they would be the screening, which involves Core Center contacting the customer themselves or the lead, in this case, I apologize for that.  Verifying their information, checking if they understand how to utilize the product, which is the stove. And then from there Core Center would then approve that lead for registration and then there's device assignment and also stock allocation to that particular customer because there is a minimum requirement upon onboarding to purchase a single 30 kg bag.  So yeah, it's a bit of a tedious process and I think that is the biggest issue we have right now is that it's difficult for us to onboard a large number of customers given a specific timeframe.  So if we set out on board, let's say 1,000 customers within a month. We would have to pull extra effort into just attaining the information, screening the customer, assigning them a device, assigning them Stoke.  And yeah, it's a bit of a tedious process for the team honestly. Okay, let's dive into the tedious.

44:21 - Alwyn van Wyk (ixo)
  First, I have a question around who does the customer onboarding, who does the visit, all of that. Is it the sales rep or is it the regenerator?

44:32 - Bupe Kalaba (supamoto.global)
  Okay, so that is the customers onboarding is done by the sales reps. Great.

44:38 - Alwyn van Wyk (ixo)
  And if you had to describe the tedious parts, starting from the top, and you mentioned backups as well, the tedious parts interacting with the system, or is it going to the customers, maybe just or a mix of those.  Maybe just talk us through what are the tedious parts that. If we had to look at the quick ones, what are those tedious parts that if that goes away, it just accelerates your onboarding process?  Okay.

45:16 - Bupe Kalaba (supamoto.global)
  I would say it's just, again, just to state that it's not really a swift process, system-wise. Physically, is something that is clearly something we can deal with.  System-wise, it's a tedious process given the fact that one, and this is, I don't think, something I was delighted with in the scope of the documentation, is that we also have a physical contract that the customer needs to read and sign.  There's the collection of information, so the KYC, have to ask them general questions, know, gender, household size, previous expenditure on charcoal, income,  just to fully grasp the demographic we're dealing with or at least have enough information to assess what makes a good and or bad customer and then after that is the screening process which is again a systems issue where the core center has to actively go through the leads so all the information that was collected by the sales rep the cost they would have to go through that to screen the information see that everything is according to standard and contact the customer immediately after and then contact the sales rep and then confirm this lead can be proceeded with and then final onboarding is done where the best is assigned and so on so I could say most of those interactions with the system where we're collecting information screening the customer and then reverting back to the customer on the screening process and devices and the stock is what takes up the book  for time.

47:03 - Alwyn van Wyk (ixo)
  Okay. Great. then just in terms of, so now once the customer is onboarded, and let's say that whole, that whole process is in place, what are the handoffs after that?  Are they, is there something that needs to be done from the sales rep to the lead generator, or from the sales rep to the stock managers, for example, to make sure that the new customer has stock available, etc.  What are the handoffs after that customer onboarding has been achieved? Okay.

47:41 - Bupe Kalaba (supamoto.global)
  So I was going to just throw a very good flow chart about that. would be the lead generator finds a list of clients that's handed off to the sales reps or the sales team who then assess that information, return back to whatever  point or the customer's location to onboard the customer. The information is, again, for a short period of time, handed off to the call center for screening.  center does that. It's handed off back to the sales reps. And then it ends from there. then before that, usually onboarding's pre-planned.  So the stoke should move before the entire event transpires. So I would say stoke. So lead generator, sales rep, sales rep.  Yeah, sales rep. then stoke provides to the sales rep. And then sales rep moves through and back to the call center.  So it's usually that back and forth there. great.

48:45 - Alwyn van Wyk (ixo)
  It just seems like there's a lot of dependency on the lead generator. So that upfront process to onboard the lead generator, if you had to go into a brand new territory where there is no super motor connection.  Is it easy to find lead generators or is that difficult? Is it a problem and quite tricky to onboard lead generators?  Or how do you normally go about that process?

49:15 - Bupe Kalaba (supamoto.global)
  Okay, so in that note, the best way and again, to all of my highlight or shed more light on this in our later discussions is picking off the already existing customer base that we have.  So let's say we've got 50 customers in, I would just say in general like in Osaka, we're going to assess our customers look for the best ones in this case and then contact them, the possibility of them becoming a lead generator and then checking off the prerequisites of our list.  Do they have enough storage? Are they known by most of the other customers? area and then move from there.  Got it.

50:05 - Alwyn van Wyk (ixo)
  Okay, great. All right. Any questions from your side for the Graham?

50:12 - Graeme Leighton
  No, not at the moment. All right.

50:16 - Alwyn van Wyk (ixo)
  Let's dive into off-boarding. So is that a process that's also quite tedious? Okay, so there are three phases, I guess, to your customers.  The one is the onboarding process where they are assessed and they get their stove and now they're in the system.  The second phase is where they are ongoing. They're actually using the stove or they're not using the stove and it's just continuous as is and that period could be very long or it could be very short.  And then there's the off-boarding process where they either give back their stove or the stove needs replacement. Actually, that's not an off-boarding.  That's more like ongoing. But the off-boarding would be... They give back the stove. In other words, they stop the contract or they are deemed not performing well enough and you repossess the stove.  Are those the only two scenarios where somebody would no longer be a customer or are there other scenarios too?

51:20 - Bupe Kalaba (supamoto.global)
  Yes, I think those are the only ones. Sorry, just to add one, there's also the loss of the product.  Again, this is in the context of are we willing to replace the stove for this customer to continue usage?  Were they a good customer to begin with or a bad customer? So there's always at least a bit of scrutinization that needs to be done before, let's say, devices replaced that case.  But I could say those are the major three phases, just an extension on the third one, on reasons for why the stove or this customer is being offered.  Okay, great.

52:03 - Alwyn van Wyk (ixo)
  But in terms of that off-boarding, is it also quite a tedious process or is that how does that process work?

52:12 - Bupe Kalaba (supamoto.global)
  Okay, so with a client off-boarding let's give let's just give the scenario of this is a customer that isn't performing well.  So what happens within the FinTech department is that we generate a report which just reflects the portfolios of the different customers in terms of utility and this is also a metric that can be put from the admin panel.  we're able to see in which I can say par the customer belongs in so the powers are broken down into different groups of categories so there's a part one to the I'll start with the first one that's on time.  So this just means the customer is on time with their purchases, they're a good customer, by every month and they haven't yet defaulted on a particular set of days because the current policies that they're supposed to, at least the current agreements that they're supposed to buy at least 30 kgs a day, which translates to 1 kg per day.  So that's on time. And then if they haven't made payments or haven't purchased pellets for a period of 1 to 30 days, they fall into the power 1 to 30 category or grouping.  And those are at least followed up by the call center group just to confirm when they'll be making payments, if they'll be making payments, is there a specific reason why they aren't making payments or monthly payments.  We have the power 30 to 60. So these ones have been probably giving either valid or invalid reasons to why they haven't been purchasing pellets.  it's because of the lack of supply from our end on some points or maybe because, again just to clarify, doesn't go beyond 30 days that we haven't supplied to them, but it does go through that stage where they haven't purchased within 30 to 60 days for particular reasons.  Maybe that time they were supposed to purchase the bag, there are a whole lot of reasons that come from Co Center.  They had a bereavement and so they couldn't really purchase anything at that time because it wasn't, it was an unfortunate event that wasn't planned for or for other reasons they just keep on giving excuses saying I'll buy next week, buy next week, I'll buy next week, so they fall within that range where we have to actively keep nudging them to either purchase a bag or tell them they'll be repossessed soon if they don't.  If they go beyond 60 to 90 or 90 plus that is when I think they fall within the range of repossession, so that means these customers  have failed to comply with the agreement and so they will have maybe will have maybe percentages contact them once tell them they will be repossessed soon confirm which days we should be able to collect the stove and send one of our field officers to collect the stove.  So that's just a brief example of the different categories that exist but I'm sure I told I will explain more on that once we have a sit down with them.

55:32 - Alwyn van Wyk (ixo)
  Okay great and when you speak about a field officer what do mean by that? What sort of role would that person be if we look at these internal roles?

55:46 - Bupe Kalaba (supamoto.global)
  Okay sorry I think most of the scope regarding these roles were with regards to the onboarding and one really considering the onboarding so but just give them context I would still put them within  films of the sales reps. Okay.

56:03 - Alwyn van Wyk (ixo)
  Yeah. Great. Great stuff. Thank you. This is very, very helpful. we're getting a lot of details. That's brilliant. So it's almost like a customer has, they've been onboarded properly.  Then during their phase of being active, then they can go through these, you call it a bar. I hear that correctly?  B-A-R?

56:26 - Bupe Kalaba (supamoto.global)
  I can see, we can call it a bar, but we'd like to call it the past status. portfolio at risk status for the customer.  Okay.

56:37 - Alwyn van Wyk (ixo)
  Portfolio at risk.

56:38 - Bupe Kalaba (supamoto.global)
  Got it.

56:39 - Alwyn van Wyk (ixo)
  Bar. Okay. So that's a green amber red. On the red, they get repossessed. Amber, they're in danger. Green, they're great.  They're doing well.

56:52 - Bupe Kalaba (supamoto.global)
  Cool.

56:53 - Alwyn van Wyk (ixo)
  That's really useful. And then off-boarding seems to be quite simple. It's just go and make a Date with them go and fetch it And the is there anything else after that?  So like any and legal Implications for them not being able to return the stove or what happens if they?

57:14 - Bupe Kalaba (supamoto.global)
  Bad or good they sold the stove or they did something with us For that one I'm not 100% sure The process flow for that I think I should I think we can just take note of that one and get up to Well, I want she joins us.  Sure.

57:33 - Alwyn van Wyk (ixo)
  Yeah, awesome all right, so you spoke about the Call center and a lot of in the last two minutes just briefly tell us about the call center and So the size of the call center at the moment how many customers they are interfacing with and the bulk of their problems Okay, so with regards to the course and I can say their interaction

58:00 - Bupe Kalaba (supamoto.global)
  is with almost all customers. Their tools range from having a bulk SMS application for easier contact of a large number of clients.  the case of, let's say, closure of a specific shop on a particular day, a supply of pellets for a particular period, maybe even just follow-ups on payments.  they use those platforms so that the bulk SMS platform and then there is the call support platform that they use.  I think it's 3CX. So they use this platform to just make calls to customers, clarify specific reasons for not making payments, for asking them or just also for conducting surveys, and just general product feedback that the customer might have for any particular repairs and or product that they receive.  So the calls come in and out, so call center would be split into teams. So there's the inbound that receives the phone calls.  So that's a group of maybe should be three, four people that receive calls. And then there's the outbound again, maybe three, four people that make calls to customers.  So it's separated within those three groups. There's also the aspects of the call center including activations. So the activations team again within the call center is in charge of, as the word says, activating or making sure the customer is able to access the product from the admin panel.  So I'm sure you are aware that the IoT board that's in the store can be either turned off and on depending on the performance of a customer.  So if a customer hasn't made payments, we can enforce a bit of... SuperMoto, I can say, sorry, that's my turn of thought there, but yeah, we can deactivate the stove for lack of payments and then we can reactivate it in the case that they need to, they have made payments and need to be put back online.  So there's usually that kind of interaction in the activations team. So a general number, honestly, I would say close to about 10, but I can get you better figures later, but that's usually their interaction with the customers.  So they're able to call in, out, they're able to make checkups and follow-ups on payments and repairs, and that is within the realm of their operations, as well as, since they fall within the sales department, they do also assess the status of the customer in general.

1:00:57 - Alwyn van Wyk (ixo)
  Brilliant, would you say the call center agents are also Are those reps, or is that a completely separate team?

1:01:04 - Bupe Kalaba (supamoto.global)
  I would like to put them as a separate team, given the fact that they are not really involved in the direct sales process, but they do age in the verification and review of client information.  So it's a 50-50 on both sides.

1:01:25 - Alwyn van Wyk (ixo)
  Brilliant. Hupe, thanks so much for your time. It's been awesome. I really enjoy working with you. And I think we're going to be creating some awesome things.  OK, so what I'll do from this transcript is I want to create a flow diagram of just everything that we've spoken about now.  And then on Monday, or even tomorrow, I'll send it out for us to grant myself you to review tomorrow.  And then on Monday, when we go into our first hour session, let's use that as our map, as our guiding light, and say, what I call to  because things want to focus on this part here. Let's highlight that, zoom in, figure out how that works and whether we can create something very quick and a quick one for that or not.  Or do we just just fix a bug, that sort of thing. Cool, other than that, next steps is we need to figure out a time and a regular time for ourselves for Monday's and Tuesday, Wednesday, Thursday and Friday, figure out that timing as well as who's gonna be in that session and setting up those meetings.  But I'll drive that from my side. Okay, great, can't wait. Awesome, sure. Great.

1:02:42 - Bupe Kalaba (supamoto.global)
  Okay, all right, thank you everyone. Have a great day.

1:02:47 - Graeme Leighton
  Thanks so much, very good to meet you. You too, thank you, bye.

1:02:51 - Bupe Kalaba (supamoto.global)
  Chacho, bye.