# 7. Success Metrics & Release Criteria 

########################################################

Define how you will measure the product's success and the **criteria for release** readiness. In software, success metrics could include both product usage goals and quality benchmarks. Release criteria are the minimum standards the product must meet in areas like functionality, performance, reliability, etc., before it can launch. This section ensures the team knows the "Definition of Done" not just for individual features, but for the product as a whole.

**What to include:** **Product success metrics** (e.g. target KPIs such as number of active users, task completion rate, NPS scores) and **Quality benchmarks** (non-functional requirements that must be met: performance thresholds, security checks, compliance requirements, etc.). You should list specific measurable targets where possible. Also include any **release gating criteria** – for example, *"All critical bugs (Sev 1) resolved, 95% of regression tests passed,"* etc. This section might be less visible to users, but it is crucial for internal alignment on what it means for the product to be "good enough" to go live. 

**Steps to set Success Metrics & Release Criteria:**  

1. **Identify Key Product Metrics:** Determine how you will know if the product is solving the intended problem and delivering value. For a new software product, this might be things like *"At least 10,000 downloads in first 3 months," "User retention rate of 30% after 1 month," "Average user logs in 3+ times per week."* If it's an internal tool, maybe *"reduces manual work by 50%"*. List 3-5 primary metrics that reflect user value or business value.  

2. **Define Quality Thresholds:** Set targets for performance and reliability. For example: *"Load time for the dashboard screen < 2 seconds on 3G networks," "System can handle 100 requests per second without errors," "99.5% uptime in first quarter."* Also consider usability and supportability: *"80% of new users complete onboarding without external help (usability)," "The app should log errors for support with sufficient detail (supportability)."* These correspond to typical **release criteria categories** like Performance, Reliability, Usability, Supportability, etc.

3. **List Must-Have Capabilities for Release:** If not already clear from features, state any functionality that is absolutely required for the initial release (MVP). *E.g.*, "User must be able to sign up and log in (cannot launch without account system)." This overlaps with prioritization, but here it's about launch go/no-go.  

4. **Specify Bug/Quality Gates:** Decide on the standards for testing. For instance: *"All critical and high-priority bugs must be fixed by release," "Test coverage of core modules > 80%," "Pass security audit by QA team."* These ensure that beyond feature completion, the product maintains quality.  

5. **Include Compliance/Approval Criteria:** If releasing a product requires regulatory approval or compliance (for example, app store guidelines, legal sign-off, or security certification), list those criteria as well. *E.g.*, "Security review by IT completed," or "App Store review passed."  

6. **Quantify When Possible:** Wherever you can, use numbers or specific conditions (thresholds, percentages, counts). This removes ambiguity – the team can objectively assess if criteria are met. For example, instead of "fast performance," say "<200ms API response time under normal load." If a qualitative metric is needed (like user satisfaction), describe how it will be measured (survey, interview, etc.).  

7. **Example Release Criteria:** To illustrate, you might include a short checklist of release criteria:  

   - *Functionality:* All core features implemented and verified (see feature list).  
   - *Performance:* Meets performance targets (page load < 2s, can handle 1k concurrent users).  
   - *Reliability:* Passes stress tests, no critical crashes in a 48-hour test run.  
   - *Security:* All user data encrypted; passes security penetration test.  
   - *Usability:* Tested with 10 users; onboarding success > 90%.  
   - *Supportability:* Monitoring and logging in place; support team trained on FAQs.  

   These are examples – tailor the list to what matters for your product.  

########################################################

Cursor Prompt (copy and paste into Cursor Composer):

Combine and polish the notes in @7-success-acceptance.md into a 'Success Metrics & Release Criteria' section. Ensure it lists clear, measurable targets for product success and quality standards for release.

########################################################