# 3. User Personas & Needs

## Primary User Personas

### 1. **"Operations Manager Olivia"** - SupaMoto Staff & Management
**Profile:** <PERSON> is a 32-year-old operations manager at SupaMoto with 5+ years experience in clean cooking distribution. She manages daily operations across multiple regions in Zambia, overseeing customer onboarding, payment processing, and agent coordination. <PERSON> has moderate technical skills and uses mobile banking regularly but is not familiar with blockchain technology.

**Goals:**
- Streamline payment processing to reduce manual workload and errors
- Gain real-time visibility into transaction status and customer payment behavior
- Automate agent commission calculations and distributions
- Ensure secure handling of large-scale voucher administration (40,000 tons equivalent)

**Pain Points:**
- Spends 60% of time on manual payment verification and reconciliation
- Cannot efficiently track payment flows across multiple agents and regions
- Experiences delays in carbon credit validation that impact cash flow planning
- Worries about security risks in current manual voucher systems

**Key Use Cases:**
- Monitor transaction dashboard for real-time payment status
- Automatically distribute commissions to field agents
- Generate financial reports for management and auditors
- Validate carbon credits instantly upon pellet purchases

### 2. **"Field Agent Felix"** - Distribution Agents & Youth Agents
**Profile:** <PERSON> is a 26-year-old community-based distribution agent in rural Zambia who sells pellets and identifies potential customers. He has basic mobile phone skills, uses mobile money regularly, and serves as a trusted intermediary between SupaMoto and local customers. <PERSON> is motivated by earning consistent income and building his reputation in the community.

**Goals:**
- Receive instant commission payments upon successful sales
- Access clear records of his sales performance and earnings
- Easily onboard new customers without complex paperwork
- Build long-term customer relationships through reliable service

**Pain Points:**
- Experiences delays in commission payments, affecting personal cash flow
- Lacks visibility into payment status and customer account balances
- Cannot efficiently track his sales performance or customer satisfaction
- Struggles with manual processes that slow down customer service

**Key Use Cases:**
- Receive automated commission payments via mobile money
- Check customer wallet balances and payment history
- Process customer top-ups and pellet orders efficiently
- Access performance dashboards showing sales metrics

### 3. **"Customer Catherine"** - End-User Households
**Profile:** Catherine is a 38-year-old mother of three in peri-urban Malawi who uses SupaMoto's clean cooking solutions for her family's daily meals. She has a basic mobile phone, uses mobile money for essential transactions, and values reliable, affordable cooking fuel. Catherine prioritizes family health and cost savings but has limited experience with digital financial services.

**Goals:**
- Easily top up her cooking fuel wallet when needed
- Track her fuel usage and budget for monthly expenses
- Receive benefits and incentives for consistent fuel purchases
- Access reliable cooking fuel without payment complications

**Pain Points:**
- Uncertainty about wallet balance and when to top up
- Difficulty understanding payment processes and transaction fees
- Limited access to customer support for payment issues
- Concerns about security of digital payment systems

**Key Use Cases:**
- Top up fuel wallet via mobile money or digital vouchers
- Check wallet balance and transaction history
- Receive automated incentives and loyalty rewards
- Get notifications for low balance or special offers

## Secondary Personas

### 4. **"Carbon Credit Buyer Carlos"** - Offtakers & Investors
**Profile:** Carlos represents institutional carbon credit buyers seeking verified, high-quality credits from clean cooking projects. He requires transparent, real-time validation and immutable transaction records for compliance and impact reporting.

**Goals:** Instant carbon credit validation, transparent impact tracking, automated credit transfers
**Key Needs:** Real-time credit issuance, immutable transaction records, compliance reporting

### 5. **"DeFi Developer Diana"** - Ecosystem Partners
**Profile:** Diana is a blockchain developer interested in building financial services on top of the SupaMoto payment infrastructure, such as microinsurance or lending products.

**Goals:** Access to transaction APIs, integration opportunities, data for financial product development
**Key Needs:** Developer documentation, API access, transaction data insights
