# 3. User Personas & Needs

########################################################

Identify **who** the product is for and what those users need to accomplish. Defining a primary user persona (or a few key personas) and their goals ensures the PRD stays customer-centric. This section builds a **shared understanding** of the target user among the team. In Agile, having the team empathize with the persona helps generate better solutions and avoids misinterpretation of requirements.


**What to include:** A profile of the primary user (or each major user type) including demographics and relevant characteristics, followed by that user's key goals and pain points related to your product domain. You might also list specific **user needs or use cases** that come directly from this persona. Keep the focus on the primary user segment that the product will serve (trying to design for everyone often leads to a diluted product). 

**Steps to create User Personas & Needs:**  

1. **Identify Primary User Persona:** Based on your research, choose the most important user group for this product – the people who will get the most value and drive success. Describe this persona's profile with concrete details: e.g. *"Tech-savvy young professionals (age 25–34) who want to improve their financial health."* Include relevant attributes like job role, skill level, environment, etc.

*For example:*

- **Persona:** *"Budgeting Bella" – 29-year-old marketing analyst, moderate tech skills, aiming to save for a house.*  
- **Characteristics:** Income level, familiarity with finance apps, habits (e.g. checks phone frequently, values simplicity).  

2.**List User Goals:** For each persona, list their main **objectives** in using your product. What is the user trying to accomplish? In our example, goals could be: *"Understand where my money goes each month," "Reduce unnecessary expenses," "Save for big purchases."* Focus on *what the user wants to achieve*, not the solution they think they need (users might jump to feature requests, but you should capture the underlying need).  

3.**Outline User Pain Points/Needs:** Document the specific needs or problems the user faces. *E.g.*, "Bella often forgets how much she has spent until the end of the month – she needs real-time feedback on spending." These needs should clearly connect to the features you'll propose.  

4.**Identify Key Use Cases or Tasks:** For each goal, describe the core **tasks** the user would perform with the product to achieve that goal. This is a bridge between user needs and features. For example: *"Set a monthly budget and get alerts when nearing the limit"* could be a task derived from the goal of reducing expenses. Remember, **tasks are not features** themselves, but what the user will do; you will define features to support these tasks.  

5.**Prioritize the Persona(s):** If you have secondary personas, mention them briefly but clarify that the product will be optimized for the primary persona first. This helps avoid trying to please everyone and ending up with scope creep.

