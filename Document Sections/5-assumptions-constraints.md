# 5. Assumptions & Constraints

########################################################

Replace the following assumptions and constraints with a clear section that documents any assumptions you are making and constraints that will shape the solution. Assumptions are things you believe to be true (about users, business, or technology) that may impact the product. Constraints are limitations or conditions that the product/design *must* work within – often these are non-functional requirements or external dependencies (technical, regulatory, etc.). Making these explicit helps prevent misunderstandings later. Agile teams revisit assumptions regularly to validate them and adjust if needed.

**Purpose of this section:** Document any **assumptions** you are making and **constraints** that will shape the solution. Assumptions are things you believe to be true (about users, business, or technology) that may impact the product. Constraints are limitations or conditions that the product/design *must* work within – often these are non-functional requirements or external dependencies (technical, regulatory, etc.). Making these explicit helps prevent misunderstandings later. Agile teams revisit assumptions regularly to validate them and adjust if needed. 

**What to include:** A bullet list of assumptions (user assumptions, business assumptions, technical assumptions) and a list of constraints and requirements that are not feature-specific. **Non-functional requirements** such as performance, security, compatibility, or compliance often appear here. Also note any **dependencies** on other systems or teams, and any design/tech constraints (for example, "must run on iOS and Android", or "must use our existing payment API"). By listing these, the team is aware of the boundaries from the start.

**Steps to capture Assumptions & Constraints:**  

1. **List Key Assumptions:** Think about what conditions need to be true for the product to succeed, which you might not have confirmed yet. Common assumptions include user behavior (e.g. "We assume users will enter transactions regularly and have internet access"), business conditions ("Pricing for the service will remain the same during launch"), or technical conditions ("We assume we can get daily data from the bank's API"). Note each assumption clearly. These will later need to be validated.  

2. **List Technical Constraints:** Identify any technology requirements or limits. For a software product, this could include required platforms or environments (*"The app must support Android 10+ and iOS 14+"*), compatibility needs (*"Must integrate with our existing backend system via REST API"*), or standards to follow (*"Follow Material Design guidelines for UI"*). Also mention if there are size or performance constraints (*"App size must be < 50MB", "Support 10,000 concurrent users"*). This ensures the development considers these from day one.

3. **List Business or Policy Constraints:** Note any non-technical constraints such as compliance requirements (*e.g.* GDPR, security standards), licensing issues, or contractual obligations. For example: *"User data must be encrypted at rest and in transit," "The system must pass a security penetration test by our Infosec team."* These are critical to avoid later blockers.  

4. **Note Dependencies:** If your product's success relies on other components or teams, list those dependencies. *For instance:* "The analytics feature depends on the Data Science team delivering an accurate prediction model," or "Feature X requires the new API from Team Y, which is scheduled for Q3." Listing dependencies helps the project manager plan around external factors.  

5. **Review and Refine:** Check that each constraint is indeed something that will constrain design/implementation (and not already covered in features). Also verify that assumptions are reasonable and note any high-risk assumptions that should be tested early. It's helpful to get input from engineers and other stakeholders to ensure all major constraints are captured.  

########################################################

Cursor Prompt (copy and paste into Cursor Composer):

Take the list of assumptions and constraints in @5-assumptions-constraints.md and rewrite them as a clear section. Separate assumptions from constraints, and phrase each item clearly (as a statement). Make sure to cover technical, user, and business aspects.

########################################################
