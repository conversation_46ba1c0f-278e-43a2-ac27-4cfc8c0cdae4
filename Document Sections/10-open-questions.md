# 10. Open Questions & Issues 

########################################################

**Purpose of this section:** Track any **unresolved questions, decisions, or risks** that came up during planning and writing the PRD. It's normal that some details are TBD – listing them ensures they aren't forgotten and provides a single spot to find them. Agile teams will typically address these open questions as they iterate. By capturing them in the PRD, you make everyone aware of what still needs to be figured out.

**What to include:** A list of questions or pending decisions that affect requirements. Each item should briefly state the question and, if possible, who is responsible or by when it should be answered. For example: "**Open Question:** Will the app support multiple currencies at launch? – (Product Manager to decide based on user research by end of Sprint 2)." You can also list known **risks or issues** (like "API rate limiting may impact real-time updates – need to confirm limits with backend team"). Essentially, any requirement-affecting issue that's not yet resolved belongs here. 

**Steps to capture Open Questions:**  

1. **Brainstorm Unresolved Topics:** Go through each section of the PRD and ask, "Is there anything here we're unsure about or need to decide later?" Common areas: unclear requirements, dependencies on feedback, technical unknowns. Jot down each as a question or statement.  

2. **Get Team Input:** Ask your team (dev, design, QA, etc.) if they have any outstanding questions about the requirements. Often engineers might say "We need to choose between Solution A or B for this feature" or designers might have "Need final decision on color scheme from brand team." These are good to capture.  

3. **Write Clear Questions/Issues:** For each, phrase it either as a question or a pending decision. Include enough context so someone reading later understands what's needed. *E.g.*, "**Q:** Do we need to support accounts with multiple users (family accounts) in v1? – This affects authentication and data sharing design."  

4. **Assign an Owner or Deadline (if known):** It helps to note who will answer the question or by when. For example: "(Owner: CTO, by Beta release)" next to a question about architecture. This isn't strictly necessary, but it encourages follow-up.  

5. **Update as Resolved:** As the project progresses, update this section by answering the questions or marking them resolved. Initially, though, keep the original questions. You might maintain a log like: "Q: X? – **Decision:** We will do Y. (Decided on 2025-02-15 in Sprint Planning)." Agile teams often handle these in backlog grooming or sprint planning, so the PRD can be updated accordingly.  

6. **Example Open Questions:**  

   - "**API Integration**: Will the banking API provide real-time updates or only daily batch? (Need confirmation from API provider) – *Impact:* real-time dashboard feature timing."  
   - "**Data Retention**: How long should we store user transaction data? (Policy decision pending with Legal) – *Impact:* non-functional reqs for storage & privacy."  
   - "**Premium Features**: Which features (if any) will be part of a paid tier versus free? (Product strategy decision) – *Impact:* may affect scope if some features move out."  

