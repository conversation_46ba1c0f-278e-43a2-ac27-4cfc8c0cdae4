# 9. Out of Scope Items 

########################################################

**Purpose of this section:** Explicitly state **what you are NOT doing** in this release or product iteration. This helps manage scope and expectations by clarifying features or requests that came up but were decided against (at least for now). It prevents stakeholders from assuming those items are covered and keeps the team focused. In an Agile setting, this is important because it's easy for scope to creep if everyone isn't clear on what ideas were tabled.

**What to include:** A bullet list of features or requirements that **will not be addressed** in this PRD. For each item, you can add a short note explaining why it's out of scope (e.g. "deferred to future version" or "not aligned with current MVP goals"). This list can include nice-to-have ideas that were consciously cut or things explicitly decided as *"not in this project."* Keep the list to significant items that someone might reasonably expect to be in scope. You don't have to list every imaginable non-feature, just those that could cause confusion if not mentioned. 

**Steps to define Out of Scope:**  

1. **Review Feature Requests and Ideas:** Think back to initial brainstorming and stakeholder inputs. Identify any popular ideas or suggested features that you decided **not** to include. For example, maybe during planning it was suggested the app also have a built-in calculator or a social sharing feature, but the team chose to exclude these for v1. List those.  

2. **Check Boundaries of Scope:** For each feature in scope, consider if there are extensions or edge aspects that are explicitly excluded. *E.g.*, if your product is a mobile app, you might say "Out of scope: Desktop application version." Or if you include expense tracking, maybe "Out of scope: automatic bill pay functionality." This draws a clear boundary around your product's functionality.  

3. **Be Clear and Specific:** Write each out-of-scope item clearly. If needed, mention scope category: *"Feature X (Out of scope):* not included because ..."* For instance: "**Investment Tracking** – *Not in scope.* This PRD focuses only on expense tracking, not managing investments or stocks (could be a separate product)."  

4. **Mention Future Consideration (if applicable):** It's okay to note if some out-of-scope items are candidates for future releases. *E.g.*, "Offline mode – Out of scope for initial release (may consider in future if user demand is high)." This tells readers the idea is known but consciously postponed.  

5. **Keep List Manageable:** Aim for a short list of the most notable exclusions (maybe 3-7 items). The goal is not to enumerate everything you won't do, but to highlight potential misunderstandings. Too many items can be confusing – focus on major points of clarification.  

6. **Consensus on Out of Scope:** Ensure all stakeholders agree these items are indeed out of scope. Surprises here can cause conflict. Use this section to drive alignment: everyone signs off that these things won't be worked on now. (This is often discussed in project kickoff or planning meetings.)  
