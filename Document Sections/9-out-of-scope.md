# 9. Out of Scope Items 

## Features Explicitly Out of Scope

### **Advanced Financial Services**
- **Cryptocurrency Trading Platform** - Not included in this release. The system focuses on stable digital vouchers and payments, not speculative trading. Cryptocurrency exchange functionality would require additional regulatory approvals and risk management systems that are beyond current scope.

- **Personal Lending & Credit Services** - Deferred to future development phase. While the transaction history will provide valuable data for credit scoring, implementing lending products requires additional regulatory compliance, risk assessment capabilities, and capital allocation that are not part of the initial infrastructure build.

- **Investment & Savings Products** - Out of scope for v1.0. Focus remains on payment processing and voucher management. Investment products would require portfolio management features, regulatory investment advisor compliance, and integration with capital markets that are not aligned with current clean cooking mission.

- **Insurance Products Integration** - Not included in initial release. While the platform could support microinsurance in the future, insurance product integration requires partnerships with licensed insurers and actuarial systems that are beyond the current project timeline.

### **Geographic & Market Expansion**
- **Additional Country Markets** - Limited to Zambia, Malawi, and Mozambique for initial launch. Expansion to other African markets (Kenya, Tanzania, Ghana) is planned for 2026 but requires separate regulatory approvals, local partnership development, and currency integration.

- **Urban Market Focus** - System optimized for rural and peri-urban markets. Urban market features like integration with formal banking systems, credit card payments, and sophisticated financial reporting are not prioritized for this release.

- **International Remittances** - Cross-border payment functionality is out of scope. The system focuses on domestic transactions within each target country. International remittance features would require additional regulatory compliance and foreign exchange capabilities.

### **Advanced Technology Features**
- **Artificial Intelligence & Machine Learning** - Advanced AI features like predictive analytics, fraud detection algorithms, and personalized recommendations are deferred to Phase 2. Basic rule-based systems will be implemented for essential functions like transaction validation.

- **Advanced Blockchain Features** - Complex DeFi protocols, yield farming, liquidity mining, and advanced smart contract functionality are not included. The system will use blockchain for transaction recording and basic smart contracts for commission distribution only.

- **IoT Device Integration** - Integration with cooking stoves, fuel sensors, or other IoT devices is out of scope. The system focuses on payment processing rather than device monitoring or automated fuel ordering based on usage patterns.

- **Advanced Analytics Dashboard** - Sophisticated business intelligence, predictive modeling, and advanced reporting features are not included in v1.0. Basic operational dashboards and standard reports will be provided, with advanced analytics planned for future releases.

### **Enterprise & B2B Features**
- **White-Label Platform** - The system is designed specifically for SupaMoto operations and is not intended as a white-label solution for other clean cooking companies. Platform-as-a-service features for third-party deployment are out of scope.

- **Advanced Partner Management** - Complex partner onboarding, multi-tier commission structures, and sophisticated partner relationship management features are simplified for initial release. Focus is on basic agent management and commission distribution.

- **Enterprise Resource Planning Integration** - Deep integration with advanced ERP systems beyond basic transaction recording is out of scope. The system will provide APIs for data export but will not replace existing ERP functionality.

### **Consumer-Facing Features**
- **Social Features & Community Building** - Social sharing, community forums, customer-to-customer interactions, and gamification features are not included. The system focuses on transactional functionality rather than social engagement.

- **Advanced Customer Support Features** - In-app chat, video support, AI chatbots, and sophisticated ticketing systems are out of scope. Basic customer support will be handled through existing call center infrastructure with system integration for account information.

- **Loyalty Program Complexity** - Advanced loyalty programs with points systems, tier-based benefits, and complex reward structures are simplified for initial release. Basic incentive distribution will be implemented with more sophisticated loyalty features planned for future development.

### **Regulatory & Compliance Extensions**
- **Advanced Compliance Reporting** - Sophisticated regulatory reporting beyond basic transaction records and carbon credit validation is out of scope. Advanced compliance features for international standards and complex regulatory frameworks are deferred.

- **Multi-Currency Support** - While the system will handle local currencies in target markets, support for multiple international currencies, foreign exchange rates, and currency hedging is not included in the initial release.

### **Infrastructure & Operations**
- **Multi-Cloud Deployment** - System will be deployed on single cloud infrastructure initially. Multi-cloud redundancy, disaster recovery across multiple cloud providers, and advanced infrastructure management are out of scope for v1.0.

- **Advanced Security Features** - While basic security measures are implemented, advanced features like biometric authentication, hardware security modules, and sophisticated fraud detection are deferred to future releases.

## Future Consideration Items

The following items are explicitly out of scope for the initial release but are being considered for future development phases based on user feedback and business needs:

- **Advanced DeFi Integration** (Q2 2026) - Yield farming, liquidity provision, and advanced DeFi protocols
- **AI-Powered Analytics** (Q3 2026) - Predictive modeling, customer behavior analysis, and automated insights
- **IoT Integration** (Q4 2026) - Smart stove connectivity and automated fuel ordering
- **Geographic Expansion** (2026-2027) - Additional African markets and international expansion
- **Advanced Financial Services** (2027) - Lending, insurance, and investment products
