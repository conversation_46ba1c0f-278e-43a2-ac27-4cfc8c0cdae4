# Example PRDs

This directory contains sample PRDs demonstrating best practices and different use cases. Use these as reference when creating your own PRD.

## Available Examples

- `portal-feature-matrixconnect/` - Portal Feature PRD
- `mobile-app/` - Mobile application PRD
- `api-service/` - REST API service PRD

Each example includes:
- Complete PRD structure
- Sample user stories
- Example requirements
- Demonstration of proper formatting
- Common diagrams and assets

## Using Examples

1. Review multiple examples to understand different approaches
2. Don't copy directly - adapt patterns to your needs
3. Pay attention to formatting and structure
4. Note how requirements link to user stories

---
*These examples follow our templates and guidelines from the root documentation.*
