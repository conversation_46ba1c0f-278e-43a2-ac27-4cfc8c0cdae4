---
description: Writing documentation rather than code
globs: 
---
Apply when using Cursor AI to write articles, rather than for coding tasks.
1. AI Usage Scope and Focus
	•	Prioritize Text Editing Over Coding – AI should focus on document writing, structuring, and refinement rather than code-related assistance.
	•	Disable Auto-Code Completions (if possible) – Prevent AI from suggesting or completing code unless explicitly requested.
	•	Limit Programming Language Suggestions – AI should not prioritize code-based responses unless the user manually enables coding support for a document.

2. Document Structuring and Formatting
	•	Ensure Proper Sectioning – AI should automatically suggest headings, subheadings, and outlines for well-structured documents.
	•	Maintain Consistent Formatting – AI should enforce proper formatting (e.g., headings, lists, bullet points, tables) based on document type.
	•	Encourage Concise and Readable Text – AI should rewrite or suggest alternatives for overly long sentences and paragraphs.

3. Writing Assistance and Language Refinement
	•	Prioritize Clarity and Grammar – AI should act as an editor first, improving grammar, spelling, and sentence clarity.
	•	Remove Jargon and Redundant Text – AI should rewrite text to be concise, professional, and easy to read.
	•	Suggest More Impactful Language – AI should enhance weak or vague phrasing to make writing stronger and more engaging.

4. AI Behavior for Content Generation
	•	Default to Text Refinement, Not Full Generation – AI should suggest edits and improvements rather than generating full sections from scratch unless specifically asked.
	•	Encourage User Input for Customization – AI should ask for clarifications before generating long-form content to ensure relevance.
	•	Verify Factual Accuracy – AI should remind users to fact-check claims when summarizing external sources or writing data-driven content.

5. Standardized Document Templates and Compliance
	•	Enforce PRD and Documentation Standards – AI should follow a structured template for PRDs, reports, and technical documents.
	•	Auto-Suggest Missing Sections – If a required section is missing (e.g., in a PRD), AI should prompt the user to add it.
	•	Check for Consistency in Terminology and Style – AI should ensure that terms, abbreviations, and phrasing are uniform across the document.

6. AI-Driven Summarization and Key Takeaways
	•	Offer Summarization as an Option – AI should provide key takeaways, summaries, and highlights for long documents.
	•	Improve Readability for Dense Text – AI should offer simplified explanations for complex or technical content.

7. AI Context and User Intent Awareness
	•	Prioritize Active User Editing Over Passive AI Suggestions – AI should assist when requested, rather than making constant, unsolicited edits.
	•	Avoid Overwriting User Input – AI should only suggest, not override, unless explicitly prompted.
	•	Recognize When Coding Is Needed – AI should still assist with code snippets if explicitly requested, but not by default.

8. AI Restriction on Code Assistance (Optional, If Needed)
	•	Disable Code Auto-Completion – Prevent AI from suggesting full code blocks in a document editor workspace.
	•	Turn Off Coding-Related Linting Rules – Ensure AI does not highlight non-coding documents with irrelevant syntax warnings.
	•	Only Generate Code When Explicitly Requested – AI should not assume a document requires code unless the user explicitly asks for it.

Implementation in Cursor Settings
	1.	Adjust AI Preferences – Modify settings to prioritize text editing instead of coding assistance.
	2.	Customize AI Prompts – Set up rules that guide AI to focus on clarity, structure, and formatting for text-based content.
	3.	Set Default Templates for PRDs, Reports, and Docs – Pre-define structured templates that AI enforces when generating content.
	4.	Limit AI’s Coding Role – If coding is unnecessary, turn off auto-completions and reduce AI’s tendency to suggest code.
